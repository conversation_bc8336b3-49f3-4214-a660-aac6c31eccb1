{"execution_id": "testsuite_execution_20250910_134347", "id": "individual", "suite_id": "individual", "suite_name": "Individual Test Case Execution", "name": "Individual Test Case Execution", "start_time": "2025-09-10T13:43:47.793380", "end_time": null, "status": "running", "testCases": [], "test_cases": [{"test_case_id": "CSUVQZ", "filename": "health2.json", "name": "health2", "description": "", "start_time": "2025-09-10T13:43:47.793843", "end_time": null, "status": "running", "steps": [], "total_steps": 6, "passed_steps": 0, "failed_steps": 0, "error_message": null}, {"test_case_id": "RUCYUJ", "filename": "apple_health_failing_20250725155727.json", "name": "apple health failing", "description": "", "start_time": "2025-09-10T13:43:47.826527", "end_time": null, "status": "running", "steps": [], "total_steps": 8, "passed_steps": 0, "failed_steps": 0, "error_message": null}, {"test_case_id": "I34NEC", "filename": "test-cleanup.json", "name": "test-cleanup", "description": "", "start_time": "2025-09-10T13:44:09.252325", "end_time": null, "status": "running", "steps": [], "total_steps": 2, "passed_steps": 0, "failed_steps": 0, "error_message": null}], "summary": {"total_tests": 3, "passed": 0, "failed": 0, "skipped": 0}}