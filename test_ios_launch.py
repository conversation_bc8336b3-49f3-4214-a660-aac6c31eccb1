#!/usr/bin/env python3
"""
Test script to verify iOS platform launch functionality
"""

import requests
import time
import socket

def test_port_availability(port):
    """Test if a port is available"""
    try:
        with socket.create_connection(('127.0.0.1', port), timeout=1):
            return True
    except Exception:
        return False

def test_ios_launch():
    """Test the complete iOS launch sequence"""
    backend_url = "http://localhost:8086"
    
    print("🧪 Testing iOS Platform Launch")
    print("=" * 50)
    
    # Step 1: Test backend health
    print("1. Testing backend health...")
    try:
        resp = requests.get(f"{backend_url}/health", timeout=5)
        if resp.status_code == 200:
            print("   ✅ Backend is healthy")
        else:
            print(f"   ❌ Backend health check failed: {resp.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Backend not reachable: {e}")
        return False
    
    # Step 2: Test login (using test credentials)
    print("2. Testing authentication...")
    try:
        login_data = {"email": "<EMAIL>", "password": "test123"}
        resp = requests.post(f"{backend_url}/api/auth/login", json=login_data, timeout=10)
        if resp.status_code == 200:
            data = resp.json()
            if data.get("success"):
                access_token = data.get("access_token")
                print("   ✅ Authentication successful")
            else:
                print(f"   ❌ Login failed: {data.get('error')}")
                return False
        else:
            print(f"   ❌ Login request failed: {resp.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return False
    
    # Step 3: Use a dummy session ID for direct platform launch
    print("3. Using direct platform launch...")
    session_id = "test_session_123"
    headers = {"Authorization": f"Bearer {access_token}"}
    print(f"   ✅ Using session ID: {session_id}")
    
    # Step 4: Check if iOS port is currently occupied
    print("4. Checking iOS port availability...")
    if test_port_availability(8090):
        print("   ⚠️  Port 8090 is currently occupied")
    else:
        print("   ✅ Port 8090 is available")
    
    # Step 5: Trigger iOS platform launch
    print("5. Triggering iOS platform launch...")
    try:
        session = requests.Session()
        session.headers.update(headers)
        resp = session.get(f"{backend_url}/app_ios/{session_id}?port=8090", 
                          timeout=30, allow_redirects=False)
        print(f"   📡 Trigger response: HTTP {resp.status_code}")
        
        if resp.status_code in (200, 302):
            print("   ✅ Platform trigger successful")
        else:
            print(f"   ❌ Platform trigger failed: {resp.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Platform trigger error: {e}")
        return False
    
    # Step 6: Wait and test port readiness
    print("6. Waiting for iOS platform to start...")
    max_wait = 30
    for i in range(max_wait):
        try:
            resp = requests.get("http://127.0.0.1:8090/health", timeout=2)
            if resp.status_code == 200:
                print(f"   ✅ iOS platform ready after {i+1} seconds")
                break
        except Exception:
            pass
        
        if i < max_wait - 1:
            print(f"   ⏳ Waiting... ({i+1}/{max_wait})")
            time.sleep(1)
    else:
        print("   ❌ iOS platform did not become ready within 30 seconds")
        return False
    
    # Step 7: Test platform functionality
    print("7. Testing iOS platform functionality...")
    try:
        resp = requests.get("http://127.0.0.1:8090/api/devices", timeout=5)
        if resp.status_code == 200:
            print("   ✅ iOS platform API responding")
        else:
            print(f"   ⚠️  iOS platform API returned: {resp.status_code}")
    except Exception as e:
        print(f"   ⚠️  iOS platform API test failed: {e}")
    
    print("\n🎉 iOS Platform Launch Test PASSED!")
    print("The iOS platform is launching correctly.")
    print("The issue is likely with the PyQt GUI's port discovery timing.")
    return True

if __name__ == "__main__":
    success = test_ios_launch()
    if not success:
        print("\n❌ Test failed - check the output above for details")
        exit(1)
    else:
        print("\n✅ All tests passed!")
