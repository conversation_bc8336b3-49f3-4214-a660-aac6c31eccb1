# Bridge module to ensure app_android imports the root-level config_android
# without accidentally importing this file itself due to sys.path order.
from __future__ import annotations

import importlib.util
from pathlib import Path

_root_cfg = Path(__file__).resolve().parent.parent / 'config_android.py'
spec = importlib.util.spec_from_file_location('root_config_android', str(_root_cfg))
if spec is None or spec.loader is None:
    raise ImportError(f'Unable to load root config_android from {_root_cfg}')
_root = importlib.util.module_from_spec(spec)
spec.loader.exec_module(_root)

# Re-export commonly used members
DIRECTORIES = _root.DIRECTORIES
FILES_TO_PUSH_DIR = getattr(_root, 'FILES_TO_PUSH_DIR', None)
GLOBAL_VALUES = getattr(_root, 'GLOBAL_VALUES', {})
DEFAULT_FLASK_PORT = getattr(_root, 'DEFAULT_FLASK_PORT', 8081)
DEFAULT_APPIUM_PORT = getattr(_root, 'DEFAULT_APPIUM_PORT', 4724)
DEFAULT_WDA_PORT = getattr(_root, 'DEFAULT_WDA_PORT', 8300)
FLASK_PORT = getattr(_root, 'FLASK_PORT', DEFAULT_FLASK_PORT)
APPIUM_PORT = getattr(_root, 'APPIUM_PORT', DEFAULT_APPIUM_PORT)
WDA_PORT = getattr(_root, 'WDA_PORT', DEFAULT_WDA_PORT)
APPIUM_CONFIG = getattr(_root, 'APPIUM_CONFIG', {})
ADB_CONFIG = getattr(_root, 'ADB_CONFIG', {})
FlaskConfig = _root.FlaskConfig
