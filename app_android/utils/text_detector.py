from utils.resource_manager import resource_manager as _rm
cv2 = _rm.opencv
import numpy as np
pytesseract = _rm.pytesseract
import logging
import os
import re
import sys
from difflib import SequenceMatcher
from typing import List, Dict, Tuple, Optional, Any

# Set up logging
logger = logging.getLogger(__name__)

# Try to import optimized text detector
OPTIMIZED_AVAILABLE = False
try:
    from .optimized_text_detector import OptimizedTextDetector, detect_text_with_tesseract_optimized
    from .ocr_config import get_ocr_config
    OPTIMIZED_AVAILABLE = True
    logger.info("Optimized text detector available")
except ImportError as e:
    logger.warning(f"Optimized text detector not available: {e}")
    OptimizedTextDetector = None
    detect_text_with_tesseract_optimized = None
    get_ocr_config = None

# Import the text detection functions from temp_text_detection
try:
    # Add the temp_text_detection directory to the path
    temp_text_detection_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'temp_text_detection')
    if temp_text_detection_dir not in sys.path:
        sys.path.append(temp_text_detection_dir)

    # Try to import the functions
    try:
        from text_detector import detect_text, detect_text_east
        logger.info("Successfully imported text detection functions from temp_text_detection")
    except ImportError:
        logger.warning("Could not import from temp_text_detection, will use built-in text detection")
except Exception as e:
    logger.error(f"Error setting up temp_text_detection import: {e}")

def detect_text_with_tesseract(image_path, text_to_find, output_dir=None, similarity_threshold=0.6, use_optimized=True):
    """
    Detect text in the image using Tesseract OCR

    Args:
        image_path (str): Path to the screenshot image
        text_to_find (str): Text to search for in the image
        output_dir (str, optional): Directory to save debug images
        similarity_threshold (float, optional): Minimum similarity score (0.0-1.0)
        use_optimized (bool, optional): Whether to use optimized implementation if available

    Returns:
        tuple: (center_x, center_y, match_info) or None if not found
    """
    logger.info(f"Detecting text '{text_to_find}' with Tesseract OCR...")
    
    # Use optimized implementation if available and requested
    if OPTIMIZED_AVAILABLE and use_optimized:
        logger.debug("Using optimized text detection")
        return detect_text_with_tesseract_optimized(image_path, text_to_find, output_dir, similarity_threshold)
    
    # Fall back to standard implementation
    logger.debug("Using standard text detection")

    # Read the image
    img = cv2.imread(image_path)
    if img is None:
        logger.error(f"Could not read image at {image_path}")
        return None

    # Get image dimensions
    height, width, _ = img.shape
    logger.info(f"Image dimensions: {width}x{height}")

    # Create a copy for visualization
    img_copy = img.copy()

    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Apply threshold to get binary image
    _, binary = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV)

    # Apply morphological operations to enhance text regions
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

    # Use Pytesseract to get detailed information about detected text
    data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)

    # Find text regions
    text_regions = []
    n_boxes = len(data['text'])

    for i in range(n_boxes):
        if int(data['conf'][i]) > 0:  # Only consider text with confidence > 0
            text = data['text'][i].strip()
            if text:  # Only consider non-empty text
                x = data['left'][i]
                y = data['top'][i]
                w = data['width'][i]
                h = data['height'][i]

                # Calculate center coordinates
                center_x = x + w // 2
                center_y = y + h // 2

                # Add to results
                text_regions.append({
                    'text': text,
                    'coordinates': {
                        'x1': x,
                        'y1': y,
                        'x2': x + w,
                        'y2': y + h,
                        'center_x': center_x,
                        'center_y': center_y,
                        'width': w,
                        'height': h
                    },
                    'confidence': int(data['conf'][i]) / 100.0
                })

                # Draw rectangle on the copy
                cv2.rectangle(img_copy, (x, y), (x + w, y + h), (0, 255, 0), 2)

                # Add text label
                cv2.putText(img_copy, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    # Save the annotated image if output_dir is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f"tesseract_annotated_{os.path.basename(image_path)}")
        cv2.imwrite(output_path, img_copy)
        logger.info(f"Saved annotated image to {output_path}")

    # Print all detected text regions
    logger.info(f"Found {len(text_regions)} text regions:")
    for i, region in enumerate(text_regions):
        logger.info(f"  Region {i+1}: '{region['text']}' (confidence: {region['confidence']:.2f})")

    # Find the region that contains the text we're looking for
    best_match = None
    best_score = 0

    for region in text_regions:
        region_text = region['text'].lower()
        search_text = text_to_find.lower()

        # Check if the region text contains our search text
        if search_text in region_text:
            score = 1.0  # Perfect match
            logger.info(f"Found exact match: '{region_text}' contains '{search_text}'")
            best_match = region
            break

        # Calculate similarity score
        score = SequenceMatcher(None, region_text, search_text).ratio()

        if score > best_score and score >= similarity_threshold:
            best_score = score
            best_match = region

    if best_match:
        logger.info(f"Best match for '{text_to_find}': '{best_match['text']}' with score {best_score:.2f}")

        # Get center coordinates
        center_x = best_match['coordinates']['center_x']
        center_y = best_match['coordinates']['center_y']

        # Draw a circle at the center point on the copy
        cv2.circle(img_copy, (center_x, center_y), 10, (0, 0, 255), -1)

        # Add text label
        cv2.putText(img_copy, f"{text_to_find} (match)", (center_x - 60, center_y - 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

        # Save the final image if output_dir is provided
        if output_dir:
            final_path = os.path.join(output_dir, f"tesseract_match_{os.path.basename(image_path)}")
            cv2.imwrite(final_path, img_copy)
            logger.info(f"Saved match image to {final_path}")

        return center_x, center_y, best_match

    logger.warning(f"No matching text found for '{text_to_find}'")
    return None

def get_text_coordinates(image_path, text_to_find, device_width=None, device_height=None, output_dir=None, similarity_threshold=0.6):
    """
    Find text in an image and return the center coordinates for tapping.

    Args:
        image_path (str): Path to the screenshot image
        text_to_find (str): Text to search for in the image
        device_width (int, optional): Actual device width for coordinate scaling
        device_height (int, optional): Actual device height for coordinate scaling
        output_dir (str, optional): Directory to save debug images
        similarity_threshold (float, optional): Minimum similarity score (0.0-1.0)

    Returns:
        tuple: (x, y) coordinates of the center of the text region, or None if not found
    """
    logger.info(f"Searching for text '{text_to_find}' in image {image_path}")

    # Method 1: Try using Tesseract OCR directly (most reliable for text detection)
    tesseract_result = detect_text_with_tesseract(image_path, text_to_find, output_dir, similarity_threshold)
    if tesseract_result:
        center_x, center_y, match = tesseract_result

        # Apply window scaling if device dimensions are provided
        if device_width and device_height:
            # Get image dimensions
            image = cv2.imread(image_path)
            img_height, img_width = image.shape[:2]

            # Calculate scaling factors
            scale_x = device_width / img_width
            scale_y = device_height / img_height

            # Apply scaling
            center_x = int(center_x * scale_x)
            center_y = int(center_y * scale_y)

            logger.info(f"Applied window scaling: ({match['coordinates']['center_x']}, {match['coordinates']['center_y']}) -> ({center_x}, {center_y})")

        return (center_x, center_y)

    # Method 2: Try using the temp_text_detection module if available
    try:
        if 'detect_text' in globals():
            logger.info("Using temp_text_detection.detect_text function")
            text_regions = detect_text(image_path, output_dir)

            # If no regions found, try EAST detector if available
            if not text_regions and 'detect_text_east' in globals():
                logger.info("No text found with basic detector, trying EAST detector")
                text_regions = detect_text_east(image_path, output_dir)

            # Find the region that contains the text we're looking for
            if text_regions:
                logger.info(f"Found {len(text_regions)} text regions")

                # Find the best match for the text we're looking for
                best_match = None
                best_score = 0

                for region in text_regions:
                    region_text = region['text'].lower()
                    search_text = text_to_find.lower()

                    # Check if the region text contains our search text
                    if search_text in region_text:
                        score = 1.0  # Perfect match
                        logger.info(f"Found exact match: '{region_text}' contains '{search_text}'")
                        best_match = region
                        break

                    # Calculate similarity score
                    score = SequenceMatcher(None, region_text, search_text).ratio()
                    logger.info(f"Text: '{region_text}', Score: {score:.2f}")

                    if score > best_score and score >= similarity_threshold:
                        best_score = score
                        best_match = region

                if best_match:
                    logger.info(f"Best match: '{best_match['text']}' with score {best_score:.2f}")

                    # Get center coordinates
                    center_x = best_match['coordinates']['center_x']
                    center_y = best_match['coordinates']['center_y']

                    # Get image dimensions
                    image = cv2.imread(image_path)
                    img_height, img_width = image.shape[:2]

                    logger.info(f"Image dimensions: {img_width}x{img_height}")
                    logger.info(f"Found text at coordinates: ({center_x}, {center_y})")

                    # Scale coordinates if device dimensions are provided
                    if device_width and device_height:
                        logger.info(f"Scaling coordinates to device dimensions: {device_width}x{device_height}")

                        # Calculate scaling factors
                        scale_x = device_width / img_width
                        scale_y = device_height / img_height

                        # Apply scaling
                        center_x = int(center_x * scale_x)
                        center_y = int(center_y * scale_y)

                        logger.info(f"Scaled coordinates: ({center_x}, {center_y})")

                    # Save debug image if output_dir is provided
                    if output_dir:
                        os.makedirs(output_dir, exist_ok=True)
                        debug_image = image.copy()

                        # Draw rectangle around the text
                        x1 = best_match['coordinates']['x1']
                        y1 = best_match['coordinates']['y1']
                        x2 = best_match['coordinates']['x2']
                        y2 = best_match['coordinates']['y2']
                        cv2.rectangle(debug_image, (x1, y1), (x2, y2), (0, 255, 0), 2)

                        # Draw a circle at the center point
                        cv2.circle(debug_image, (center_x, center_y), 5, (0, 0, 255), -1)

                        # Add text labels
                        cv2.putText(debug_image, f"Text: {text_to_find}", (10, 30),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                        cv2.putText(debug_image, f"Coords: ({center_x}, {center_y})", (10, 60),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

                        # Save the image
                        output_path = os.path.join(output_dir, f"text_detection_{os.path.basename(image_path)}")
                        cv2.imwrite(output_path, debug_image)
                        logger.info(f"Saved debug image to {output_path}")

                    return (center_x, center_y)
                else:
                    logger.warning(f"No matching text found for '{text_to_find}'")
            else:
                logger.warning("No text regions found in the image")
    except Exception as e:
        logger.error(f"Error using temp_text_detection: {e}")

    # Method 3: Fallback to using the built-in TextDetector
    logger.info("Falling back to built-in TextDetector")
    detector = TextDetector()

    # Find all matching text regions
    matches = detector.find_text(image_path, text_to_find, similarity_threshold)

    if not matches:
        logger.warning(f"Text '{text_to_find}' not found in image with built-in detector")
        return None

    # Get the first match (assuming it's the most relevant)
    x1, y1, x2, y2 = matches[0]

    # Calculate center coordinates
    center_x = (x1 + x2) // 2
    center_y = (y1 + y2) // 2

    # Get image dimensions
    image = cv2.imread(image_path)
    img_height, img_width = image.shape[:2]

    logger.info(f"Image dimensions: {img_width}x{img_height}")
    logger.info(f"Found text at coordinates: ({center_x}, {center_y})")

    # Scale coordinates if device dimensions are provided
    if device_width and device_height:
        logger.info(f"Scaling coordinates to device dimensions: {device_width}x{device_height}")

        # Calculate scaling factors
        scale_x = device_width / img_width
        scale_y = device_height / img_height

        # Apply scaling
        center_x = int(center_x * scale_x)
        center_y = int(center_y * scale_y)

        logger.info(f"Scaled coordinates: ({center_x}, {center_y})")

    # Save debug image if output_dir is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        debug_image = image.copy()

        # Draw rectangle around the text
        cv2.rectangle(debug_image, (x1, y1), (x2, y2), (0, 255, 0), 2)

        # Draw a circle at the center point
        cv2.circle(debug_image, (center_x, center_y), 5, (0, 0, 255), -1)

        # Add text labels
        cv2.putText(debug_image, f"Text: {text_to_find}", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        cv2.putText(debug_image, f"Coords: ({center_x}, {center_y})", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

        # Save the image
        output_path = os.path.join(output_dir, f"text_detection_{os.path.basename(image_path)}")
        cv2.imwrite(output_path, debug_image)
        logger.info(f"Saved debug image to {output_path}")

    return (center_x, center_y)

class TextDetector:
    """Class for detecting text in images using OCR"""

    def __init__(self, use_optimized=True):
        """Initialize the text detector
        
        Args:
            use_optimized (bool): Whether to use optimized implementation if available
        """
        self.logger = logging.getLogger("TextDetector")
        self.use_optimized = use_optimized and OPTIMIZED_AVAILABLE
        
        if self.use_optimized:
            self.logger.info("Initializing with optimized text detector")
            self.optimized_detector = OptimizedTextDetector()
        else:
            self.logger.info("Initializing with standard text detector")
            self.optimized_detector = None

        # Set Tesseract path if needed (uncomment and modify for your system)
        # pytesseract.pytesseract.tesseract_cmd = r'/usr/local/bin/tesseract'

        # Configure Tesseract parameters
        self.config = '--oem 3 --psm 11'  # OEM 3 = Default OCR engine, PSM 11 = Sparse text

    def find_text(self, image_path, text_to_find, similarity_threshold=0.7):
        """
        Find text in an image and return the bounding box coordinates

        Args:
            image_path: Path to the image file
            text_to_find: Text to search for in the image
            similarity_threshold: Minimum similarity score to consider a match (0.0-1.0)

        Returns:
            List of bounding boxes (x1, y1, x2, y2) for matching text regions
        """
        # Use optimized implementation if available
        if self.use_optimized and self.optimized_detector:
            try:
                matches = self.optimized_detector.find_text(image_path, text_to_find, similarity_threshold)
                # Convert optimized format to standard format
                bboxes = []
                for match in matches:
                    bbox = match['bbox']
                    x1, y1 = bbox['x'], bbox['y']
                    x2, y2 = x1 + bbox['width'], y1 + bbox['height']
                    bboxes.append((x1, y1, x2, y2))
                return bboxes
            except Exception as e:
                self.logger.warning(f"Optimized detection failed, falling back to standard: {e}")
        
        # Standard implementation
        if not os.path.exists(image_path):
            self.logger.error(f"Image file not found: {image_path}")
            return []

        try:
            # Read the image
            image = cv2.imread(image_path)
            if image is None:
                self.logger.error(f"Failed to read image: {image_path}")
                return []

            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Apply thresholding to get a binary image
            _, binary = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

            # Perform OCR on the image
            data = pytesseract.image_to_data(gray, config=self.config, output_type=pytesseract.Output.DICT)

            # Find matching text regions
            matches = []
            n_boxes = len(data['text'])

            for i in range(n_boxes):
                # Skip empty text
                if not data['text'][i].strip():
                    continue

                # Calculate similarity between detected text and target text
                similarity = self._calculate_similarity(data['text'][i], text_to_find)

                # If similarity is above threshold, consider it a match
                if similarity >= similarity_threshold:
                    self.logger.info(f"Found text match: '{data['text'][i]}' (similarity: {similarity:.2f})")

                    # Get bounding box coordinates
                    x = data['left'][i]
                    y = data['top'][i]
                    w = data['width'][i]
                    h = data['height'][i]

                    # Add to matches list
                    matches.append((x, y, x + w, y + h))

            # If no direct matches found, try to find partial matches
            if not matches:
                self.logger.info("No direct matches found, trying to find partial matches...")
                matches = self._find_partial_matches(data, text_to_find, similarity_threshold)

            return matches
        except Exception as e:
            self.logger.error(f"Error in standard text detection: {e}")
            return []
    
    def get_performance_stats(self) -> Optional[Dict[str, Any]]:
        """
        Get performance statistics from the optimized detector
        
        Returns:
            Dictionary with performance stats or None if not using optimized detector
        """
        if self.use_optimized and self.optimized_detector:
            return self.optimized_detector.get_cache_stats()
        return None
    
    def clear_cache(self):
        """
        Clear the OCR cache if using optimized detector
        """
        if self.use_optimized and self.optimized_detector:
            self.optimized_detector.clear_cache()
            self.logger.info("OCR cache cleared")
    
    def list_optimized_configurations(self) -> Optional[Dict[str, str]]:
        """
        List available optimized configurations
        
        Returns:
            Dictionary of available configurations or None if not using optimized detector
        """
        if self.use_optimized and self.optimized_detector:
            return self.optimized_detector.list_configurations()
        return None
    
    def batch_find_text(self, 
                       image_paths: List[str], 
                       text_to_find: str,
                       similarity_threshold: float = 0.7,
                       max_workers: int = 4) -> Dict[str, List[Tuple]]:
        """
        Process multiple images in parallel (optimized detector only)
        
        Args:
            image_paths: List of image file paths
            text_to_find: Text to search for
            similarity_threshold: Minimum similarity score
            max_workers: Maximum number of worker threads
            
        Returns:
            Dictionary mapping image paths to their bounding box results
        """
        try:
            if self.use_optimized and self.optimized_detector:
                # Use optimized batch processing
                results = self.optimized_detector.batch_find_text(
                    image_paths, text_to_find, similarity_threshold, max_workers
                )
                
                # Convert to standard format
                converted_results = {}
                for image_path, matches in results.items():
                    bboxes = []
                    for match in matches:
                        bbox = match['bbox']
                        x1, y1 = bbox['x'], bbox['y']
                        x2, y2 = x1 + bbox['width'], y1 + bbox['height']
                        bboxes.append((x1, y1, x2, y2))
                    converted_results[image_path] = bboxes
                
                return converted_results
            else:
                # Fall back to sequential processing
                results = {}
                for image_path in image_paths:
                    results[image_path] = self.find_text(image_path, text_to_find, similarity_threshold)
                return results
        except Exception as e:
            self.logger.error(f"Error in batch text finding: {e}")
            return {}

    def _calculate_similarity(self, text1, text2):
        """
        Calculate similarity between two strings

        Args:
            text1: First string
            text2: Second string

        Returns:
            Similarity score between 0.0 and 1.0
        """
        # Convert to lowercase for case-insensitive comparison
        text1 = text1.lower()
        text2 = text2.lower()

        # Use SequenceMatcher for fuzzy matching
        return SequenceMatcher(None, text1, text2).ratio()

    def _find_partial_matches(self, data, text_to_find, similarity_threshold):
        """
        Find partial matches in OCR data

        Args:
            data: OCR data dictionary
            text_to_find: Text to search for
            similarity_threshold: Minimum similarity score

        Returns:
            List of bounding boxes for matching regions
        """
        matches = []
        text_to_find_lower = text_to_find.lower()

        # Try to find the text in consecutive words
        n_boxes = len(data['text'])
        for i in range(n_boxes - 1):
            if not data['text'][i].strip():
                continue

            # Start with current word
            combined_text = data['text'][i].lower()
            combined_boxes = [(i, data['left'][i], data['top'][i],
                              data['width'][i], data['height'][i])]

            # Try adding consecutive words
            for j in range(i + 1, min(i + 5, n_boxes)):  # Look at up to 5 consecutive words
                if not data['text'][j].strip():
                    continue

                # Add next word with a space
                combined_text += " " + data['text'][j].lower()
                combined_boxes.append((j, data['left'][j], data['top'][j],
                                      data['width'][j], data['height'][j]))

                # Check similarity
                similarity = self._calculate_similarity(combined_text, text_to_find_lower)
                if similarity >= similarity_threshold:
                    self.logger.info(f"Found partial match: '{combined_text}' (similarity: {similarity:.2f})")

                    # Calculate bounding box that encompasses all matched words
                    min_x = min(box[1] for box in combined_boxes)
                    min_y = min(box[2] for box in combined_boxes)
                    max_x = max(box[1] + box[3] for box in combined_boxes)
                    max_y = max(box[2] + box[4] for box in combined_boxes)

                    matches.append((min_x, min_y, max_x, max_y))
                    break

        return matches
