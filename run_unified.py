#!/usr/bin/env python
"""
Mobile App Automation Tool - Unified Entry Point
This script serves as the main entry point for the standalone executable,
handling both iOS and Android platforms.
"""
import os
import sys
import signal
import subprocess
import time
import logging
import argparse
from importlib import import_module
from typing import Dict, Optional, Tuple

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UnifiedAppManager:
    """Manages both iOS and Android automation instances"""
    
    def __init__(self):
        self.root_dir = os.path.dirname(os.path.abspath(__file__))
        self.ios_app_dir = os.path.join(self.root_dir, 'app')
        self.android_app_dir = os.path.join(self.root_dir, 'app_android')
        self.device_controllers = {}
        self.flask_apps = {}
        
    def _setup_paths(self):
        """Set up Python paths for imports"""
        paths = [self.root_dir, self.ios_app_dir, self.android_app_dir]
        for path in paths:
            if path not in sys.path:
                sys.path.insert(0, path)
    
    def _load_dynamic_port_initializer(self):
        """Load the dynamic port initialization module"""
        try:
            from utils.dynamic_port_init import initialize_platform_ports
            return initialize_platform_ports
        except ModuleNotFoundError as e:
            logger.warning(f"Dynamic port initialization not available: {e}")
            return None
    
    def _allocate_ports(self, platform: str, custom_ports: Optional[Dict] = None) -> Dict[str, int]:
        """Allocate ports for the specified platform"""
        default_ports = {
            'ios': {
                'flask': 8080,
                'appium': 4723,
                'wda': 8100
            },
            'android': {
                'flask': 8081,
                'appium': 4724,
                'wda': 8300
            }
        }
        
        if custom_ports is None:
            custom_ports = {}
            
        # Try dynamic port allocation first
        init_ports = self._load_dynamic_port_initializer()
        if init_ports:
            try:
                return init_ports(platform, custom_ports)
            except Exception as e:
                logger.warning(f"Dynamic port allocation failed: {e}")
        
        # Fall back to default or custom ports
        platform_defaults = default_ports[platform]
        return {
            'flask': custom_ports.get('flask', platform_defaults['flask']),
            'appium': custom_ports.get('appium', platform_defaults['appium']),
            'wda': custom_ports.get('wda', platform_defaults['wda'])
        }
    
    def _initialize_device_controller(self, platform: str, appium_port: int) -> object:
        """Initialize the appropriate device controller"""
        if platform == 'ios':
            from app.utils.appium_device_controller import AppiumDeviceController
        else:
            from app_android.utils.appium_device_controller import AppiumDeviceController
        
        return AppiumDeviceController(appium_port=appium_port)
    
    def _initialize_flask_app(self, platform: str, port: int, device_controller: object) -> Tuple[object, object]:
        """Initialize Flask app for the specified platform"""
        if platform == 'ios':
            import app as flask_app
            from app import config
        else:
            import app_android.app as flask_app
            from app_android import config_android as config
            
        app = flask_app.app
        config.FLASK_PORT = port
        config.APPIUM_PORT = device_controller.appium_port
        flask_app.device_controller = device_controller
        
        return app, config
    
    def start_platform(self, platform: str, custom_ports: Optional[Dict] = None) -> None:
        """Start automation for the specified platform"""
        # Allocate ports
        ports = self._allocate_ports(platform, custom_ports)
        
        # Initialize device controller
        device_controller = self._initialize_device_controller(platform, ports['appium'])
        self.device_controllers[platform] = device_controller
        
        # Initialize Flask app
        app, config = self._initialize_flask_app(platform, ports['flask'], device_controller)
        self.flask_apps[platform] = app
        
        # Set migration mode flag
        os.environ[f"{platform.upper()}_MIGRATION_MODE"] = 'true'
        
        # Print startup information
        platform_name = "iOS" if platform == "ios" else "Android"
        print(f"Starting Mobile App Automation Tool ({platform_name})...")
        print(f"Configuration:")
        print(f"  - Flask server port: {ports['flask']}")
        print(f"  - Appium server port: {ports['appium']}")
        print(f"  - WebDriverAgent port: {ports['wda']}")
        print(f"Open your web browser and navigate to: http://localhost:{ports['flask']}")
        
        # Start Flask app
        SECURE = os.getenv('SECURE_BUILD', 'False').lower() == 'true'
        host = '127.0.0.1' if SECURE else '0.0.0.0'
        debug = False if SECURE else True
        
        # Start in a new process to allow multiple platforms
        if os.fork() == 0:  # Child process
            app.run(debug=debug, use_reloader=False, host=host, port=ports['flask'])
            os._exit(0)
    
    def start(self, platforms: list, custom_ports: Optional[Dict] = None) -> None:
        """Start automation for specified platforms"""
        self._setup_paths()
        
        for platform in platforms:
            if platform not in ['ios', 'android']:
                logger.error(f"Invalid platform: {platform}")
                continue
            self.start_platform(platform, custom_ports)
    
    def shutdown(self) -> None:
        """Shutdown all running platforms"""
        for platform, controller in self.device_controllers.items():
            try:
                controller.shutdown()
                logger.info(f"Shut down {platform} device controller")
            except Exception as e:
                logger.error(f"Error shutting down {platform} device controller: {e}")

def signal_handler(sig, frame):
    """Handle shutdown signals"""
    print("\nShutting down and cleaning up...")
    if hasattr(signal_handler, 'app_manager'):
        signal_handler.app_manager.shutdown()
    sys.exit(0)

def main():
    parser = argparse.ArgumentParser(description='Mobile App Automation Tool - Unified Launcher')
    parser.add_argument('--platforms', nargs='+', choices=['ios', 'android'], 
                      default=['ios', 'android'],
                      help='Platforms to start (default: both)')
    parser.add_argument('--ios-port', type=int, help='iOS Flask server port')
    parser.add_argument('--android-port', type=int, help='Android Flask server port')
    parser.add_argument('--ios-appium-port', type=int, help='iOS Appium server port')
    parser.add_argument('--android-appium-port', type=int, help='Android Appium server port')
    args = parser.parse_args()
    
    # Configure custom ports if specified
    custom_ports = {}
    if args.ios_port:
        custom_ports['ios_flask'] = args.ios_port
    if args.android_port:
        custom_ports['android_flask'] = args.android_port
    if args.ios_appium_port:
        custom_ports['ios_appium'] = args.ios_appium_port
    if args.android_appium_port:
        custom_ports['android_appium'] = args.android_appium_port
    
    # Set up signal handlers
    app_manager = UnifiedAppManager()
    signal_handler.app_manager = app_manager
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Start platforms
    try:
        app_manager.start(args.platforms, custom_ports if custom_ports else None)
        
        # Keep main process running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nShutdown requested...")
        app_manager.shutdown()
        sys.exit(0)

if __name__ == "__main__":
    main()