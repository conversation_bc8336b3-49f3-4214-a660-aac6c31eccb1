2025-09-01 21:19:09,123 - __main__ - WARNING - Dynamic port allocation failed: No module named 'utils.dynamic_port_init'. Using fallback ports.
2025-09-01 21:19:09,123 - __main__ - INFO - Android app configured to use only the 5 specified database files in app_android/data/
2025-09-01 21:19:09,131 - app_android.utils.database - INFO - Android migration mode - skipping database initialization
2025-09-01 21:19:09,131 - app_android.utils.database - INFO - Checking initial database state...
2025-09-01 21:19:09,132 - app_android.utils.database - ERROR - Error checking database state: no such column: timestamp
2025-09-01 21:19:09,132 - app_android.utils.directory_paths_db - INFO - Android DirectoryPathsDB initialized with: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/data/android_settings.db
2025-09-01 21:19:09,132 - app_android.utils.directory_paths_db - INFO - Android DirectoryPathsDB initialized with: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/data/android_settings.db
2025-09-01 21:19:09,132 - app_android.config_android - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-09-01 21:19:09,133 - app_android.config_android - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/android_data/reports
2025-09-01 21:19:09,133 - app_android.config_android - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
2025-09-01 21:19:09,133 - app_android.config_android - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-09-01 21:19:09,134 - app_android.config_android - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-09-01 21:19:09,134 - app_android.config_android - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/reports_ios/suites
2025-09-01 21:19:09,134 - app_android.config_android - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/recordings_ios
2025-09-01 21:19:09,135 - app_android.config_android - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/custom_temp_android
2025-09-01 21:19:09,135 - app_android.config_android - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/android_data/files_to_push
2025-09-01 21:19:09,135 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4724, WDA: 8300) - preserving existing processes for multi-instance support
2025-09-01 21:19:09,135 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-09-01 21:19:09,778 - app_android.utils.global_values_db - INFO - Android migration mode - skipping global values database initialization
2025-09-01 21:19:09,779 - utils.directory_paths_db - INFO - iOS DirectoryPathsDB initialized with: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app/data/ios_settings.db
2025-09-01 21:19:09,780 - utils.directory_paths_db - INFO - iOS DirectoryPathsDB initialized with: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app/data/ios_settings.db
2025-09-01 21:19:09,780 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/ios_data/test_cases
2025-09-01 21:19:09,780 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/ios_data/reports
2025-09-01 21:19:09,781 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
2025-09-01 21:19:09,781 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/ios_data/reference_images
2025-09-01 21:19:09,782 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/ios_data/test_suites
2025-09-01 21:19:09,782 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/reports_ios/suites
2025-09-01 21:19:09,782 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/recordings_ios
2025-09-01 21:19:09,783 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/my_custom_temp_ios
2025-09-01 21:19:09,783 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/ios_data/files_to_push
2025-09-01 21:19:09,783 - app_android.utils.global_values_db - INFO - Using global values from config.py
2025-09-01 21:19:09,783 - app_android.utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-09-01 21:19:10,055 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-09-01 21:19:10,090 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-09-01 21:19:10,374 - app_android.utils.directory_paths_db - INFO - Android DirectoryPathsDB initialized with: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/data/android_settings.db
2025-09-01 21:19:10,374 - config_android - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-09-01 21:19:10,375 - config_android - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/android_data/reports
2025-09-01 21:19:10,375 - config_android - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
2025-09-01 21:19:10,376 - config_android - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-09-01 21:19:10,376 - config_android - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-09-01 21:19:10,376 - config_android - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/reports_ios/suites
2025-09-01 21:19:10,377 - config_android - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/recordings_ios
2025-09-01 21:19:10,377 - config_android - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/custom_temp_android
2025-09-01 21:19:10,377 - config_android - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/android_data/files_to_push
2025-09-01 21:19:10,435 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-09-01 21:19:10,436 - utils.database - INFO - Test_steps table schema updated successfully
2025-09-01 21:19:10,436 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-09-01 21:19:10,437 - utils.database - INFO - Screenshots table schema updated successfully
2025-09-01 21:19:10,437 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-09-01 21:19:10,437 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-09-01 21:19:10,437 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-09-01 21:19:10,437 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-09-01 21:19:10,438 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-09-01 21:19:10,438 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-09-01 21:19:10,438 - utils.database - INFO - Database initialized successfully
2025-09-01 21:19:10,438 - utils.database - INFO - Checking initial database state...
2025-09-01 21:19:10,438 - utils.database - INFO - Database state: 16 suites, 60 cases, 0 steps, 0 screenshots, 0 tracking entries
2025-09-01 21:19:10,439 - utils.database - INFO - Sample suites: [('EZyIO4', 'WW', 'active', '2025-08-30 05:45:29'), ('vuYvvC', 'TC3', 'active', '2025-08-30 05:45:29'), ('ReLhSZ', 'Kmart AU Android', 'active', '2025-08-30 05:45:29')]
2025-09-01 21:19:10,449 - app_android.test_suites_manager - INFO - Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/data/android_test_suites.db
2025-09-01 21:19:10,449 - app_android.utils.test_case_manager - INFO - TestCaseManager initialized with directory: /Users/<USER>/Documents/automation-tool/ios_data/test_cases
2025-09-01 21:19:10,449 - app_android.utils.test_case_manager - INFO - Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/data/android_test_suites.db
2025-09-01 21:19:10,449 - test_suites_manager - INFO - Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/data/android_test_suites.db
2025-09-01 21:19:10,450 - app - INFO - Using directories from config_android.py:
2025-09-01 21:19:10,450 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-09-01 21:19:10,450 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-09-01 21:19:10,450 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
[2025-09-01 21:19:10,515] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4724, WDA port: 8300
[2025-09-01 21:19:10,529] INFO in appium_device_controller: Appium server is running and ready on port 4724
[2025-09-01 21:19:10,529] INFO in appium_device_controller: Appium server is already running and responsive
[2025-09-01 21:19:10,532] INFO in appium_device_controller: Appium server is running and ready on port 4724
[2025-09-01 21:19:10,532] INFO in appium_device_controller: Appium server is already running and responsive
[2025-09-01 21:19:10,532] INFO in optimized_session_manager: OptimizedSessionManager initialized
[2025-09-01 21:19:10,532] INFO in appium_device_controller: Optimized session manager initialized
✅ Loaded environment from /Users/<USER>/Documents/automation-tool/MobileAppAutomation/.env
Starting Mobile App Automation Tool (Android)...
Configuration:
  - Flask server port: 8081
  - Appium server port: 4724
  - WebDriverAgent port: 8300
Open your web browser and navigate to: http://localhost:8081
 * Serving Flask app 'app'
 * Debug mode: on
[2025-09-01 21:19:10,602] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://**************:8081
[2025-09-01 21:19:10,603] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-09-01 21:19:16,570] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET / HTTP/1.1" 200 -
[2025-09-01 21:19:16,600] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,609] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /static/js/modules/actionFormManager.js?v=1756728556 HTTP/1.1" 200 -
[2025-09-01 21:19:16,610] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /static/js/modules/reportAndFormUtils.js?v=1756728556 HTTP/1.1" 200 -
[2025-09-01 21:19:16,610] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,611] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /static/js/modules/uiUtils.js?v=1756728556 HTTP/1.1" 200 -
[2025-09-01 21:19:16,611] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,613] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,617] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,621] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,624] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/css/detachable-device-screen.css HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,626] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,630] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,631] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/css/bulk-locator-manager.css HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,633] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,635] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,637] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/bulk-locator-manager.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,645] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /static/js/action-manager.js?v=1756728556 HTTP/1.1" 200 -
[2025-09-01 21:19:16,647] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,648] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,651] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/detachable-device-screen.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,652] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,657] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,666] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,668] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,673] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,673] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /static/js/action-description.js?v=1756728556 HTTP/1.1" 200 -
[2025-09-01 21:19:16,683] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,686] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,687] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,689] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,698] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,700] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,705] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,707] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /static/js/main.js?v=1756728556 HTTP/1.1" 200 -
[2025-09-01 21:19:16,708] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,708] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,727] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-09-01 21:19:16,731] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/environments HTTP/1.1" 200 -
[2025-09-01 21:19:16,742] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/bulk_locator/backups HTTP/1.1" 200 -
[2025-09-01 21:19:16,743] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/directory_paths HTTP/1.1" 200 -
[2025-09-01 21:19:16,747] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-09-01 21:19:16,754] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/reference_images HTTP/1.1" 200 -
[2025-09-01 21:19:16,759] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-09-01 21:19:16,767] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-09-01 21:19:16,773] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-09-01 21:19:16,781] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-09-01 21:19:16,782] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/environments/current HTTP/1.1" 200 -
[2025-09-01 21:19:16,789] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/settings HTTP/1.1" 200 -
[2025-09-01 21:19:16,794] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/reference_images HTTP/1.1" 200 -
[2025-09-01 21:19:16,801] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/reference_images HTTP/1.1" 200 -
[2025-09-01 21:19:16,813] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/reference_images HTTP/1.1" 200 -
[2025-09-01 21:19:16,821] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/reference_images HTTP/1.1" 200 -
[2025-09-01 21:19:16,828] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/environments/1/variables HTTP/1.1" 200 -
[2025-09-01 21:19:16,831] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/reference_images HTTP/1.1" 200 -
[2025-09-01 21:19:16,856] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/test_cases/files HTTP/1.1" 200 -
[2025-09-01 21:19:16,865] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-09-01 21:19:16,949] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:16] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-09-01 21:19:17,057] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:17] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-09-01 21:19:17,969] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:17] "GET /api/devices HTTP/1.1" 200 -
[2025-09-01 21:19:19,521] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4724, WDA port: 8300
[2025-09-01 21:19:19,527] INFO in appium_device_controller: Appium server is running and ready on port 4724
[2025-09-01 21:19:19,527] INFO in appium_device_controller: Appium server is already running and responsive
[2025-09-01 21:19:19,532] INFO in appium_device_controller: Appium server is running and ready on port 4724
[2025-09-01 21:19:19,532] INFO in appium_device_controller: Appium server is already running and responsive
[2025-09-01 21:19:19,532] INFO in optimized_session_manager: OptimizedSessionManager initialized
[2025-09-01 21:19:19,532] INFO in appium_device_controller: Optimized session manager initialized
[2025-09-01 21:19:19,533] INFO in appium_device_controller: Connecting to device: PJTCI7EMSSONYPU8 with options: None, platform hint: Android
[2025-09-01 21:19:19,533] DEBUG in appium_device_controller: AppiumDeviceManager integration not available
[2025-09-01 21:19:19,533] INFO in appium_device_controller: AppiumDeviceManager connection failed, falling back to direct connection
[2025-09-01 21:19:19,533] INFO in appium_device_controller: Connection attempt 1/3
[2025-09-01 21:19:19,533] INFO in appium_device_controller: Using provided platform hint: Android
[2025-09-01 21:19:19,533] INFO in appium_device_controller: Added Android-specific UiAutomator2 capabilities with enhanced stability settings
[2025-09-01 21:19:19,533] INFO in appium_device_controller: Desired capabilities: {'platformName': 'Android', 'deviceName': 'PJTCI7EMSSONYPU8', 'udid': 'PJTCI7EMSSONYPU8', 'newCommandTimeout': 600, 'noReset': True, 'automationName': 'UiAutomator2', 'uiautomator2ServerLaunchTimeout': 120000, 'uiautomator2ServerInstallTimeout': 120000, 'adbExecTimeout': 120000, 'skipServerInstallation': False, 'skipDeviceInitialization': False, 'ignoreHiddenApiPolicyError': True, 'disableWindowAnimation': True, 'autoGrantPermissions': True, 'dontStopAppOnReset': True, 'unicodeKeyboard': True, 'resetKeyboard': True, 'skipLogcatCapture': True, 'enforceXPath1': True, 'eventTimings': True, 'printPageSourceOnFindFailure': False, 'shouldTerminateApp': False, 'forceAppLaunch': False, 'systemPort': 8201, 'mjpegServerPort': 7811, 'clearSystemFiles': True, 'skipUnlock': True}
[2025-09-01 21:19:19,533] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'Android', 'appium:deviceName': 'PJTCI7EMSSONYPU8', 'appium:udid': 'PJTCI7EMSSONYPU8', 'appium:newCommandTimeout': 600, 'appium:noReset': True, 'appium:automationName': 'UiAutomator2', 'appium:uiautomator2ServerLaunchTimeout': 120000, 'appium:uiautomator2ServerInstallTimeout': 120000, 'appium:adbExecTimeout': 120000, 'appium:skipServerInstallation': False, 'appium:skipDeviceInitialization': False, 'appium:ignoreHiddenApiPolicyError': True, 'appium:disableWindowAnimation': True, 'appium:autoGrantPermissions': True, 'appium:dontStopAppOnReset': True, 'appium:unicodeKeyboard': True, 'appium:resetKeyboard': True, 'appium:skipLogcatCapture': True, 'appium:enforceXPath1': True, 'appium:eventTimings': True, 'appium:printPageSourceOnFindFailure': False, 'appium:shouldTerminateApp': False, 'appium:forceAppLaunch': False, 'appium:systemPort': 8201, 'appium:mjpegServerPort': 7811, 'appium:clearSystemFiles': True, 'appium:skipUnlock': True}
[2025-09-01 21:19:19,533] INFO in appium_device_controller: Connection attempt 1/3
[2025-09-01 21:19:19,536] WARNING in grid_config: Grid not available, falling back to direct connection for android
[2025-09-01 21:19:19,536] INFO in appium_device_controller: Using connection URL: http://127.0.0.1:4724/wd/hub
[2025-09-01 21:19:30,387] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:30] "GET /api/reference_images HTTP/1.1" 200 -
[2025-09-01 21:19:30,531] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:30] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-09-01 21:19:30,538] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:30] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-09-01 21:19:36,480] INFO in appium_device_controller: Connection verified with capabilities: Android
[2025-09-01 21:19:36,480] INFO in appium_device_controller: Initializing platform helpers for Android
[2025-09-01 21:19:36,480] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-09-01 21:19:36,496] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-09-01 21:19:36,496] INFO in appium_device_controller: Device dimensions: (1080, 2400)
[2025-09-01 21:19:36,496] INFO in appium_device_controller: Using native Appium screenshots for Android device: PJTCI7EMSSONYPU8
[2025-09-01 21:19:36,496] INFO in appium_device_controller: Initializing Android-specific helpers
[2025-09-01 21:19:36,496] INFO in appium_device_controller: Android version: 12.0
[2025-09-01 21:19:36,496] INFO in appium_device_controller: Setting up UiAutomator2 support
[2025-09-01 21:19:36,562] INFO in appium_device_controller: ADB shell access confirmed
[2025-09-01 21:19:36,562] INFO in appium_device_controller: Attempting to initialize UIAutomator2 helper for device: PJTCI7EMSSONYPU8
[2025-09-01 21:19:36,563] DEBUG in appium_device_controller: UIAutomator2Helper class imported successfully
[2025-09-01 21:19:36,662] INFO in uiautomator2_helper: ADB connection confirmed for UIAutomator2 helper (device: PJTCI7EMSSONYPU8)
[2025-09-01 21:19:36,663] INFO in appium_device_controller: UIAutomator2 helper initialized successfully for device: PJTCI7EMSSONYPU8
[2025-09-01 21:19:36,663] INFO in appium_device_controller: UIAutomator2 helper is ready and ADB connection confirmed
[2025-09-01 21:19:36,663] INFO in appium_device_controller: Platform helpers initialization completed
[2025-09-01 21:19:36,663] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-09-01 21:19:36,663] INFO in appium_device_controller: Connection monitoring started
[2025-09-01 21:19:36,667] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,667] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,667] INFO in player: Step retry and rerun functionality initialized
[2025-09-01 21:19:36,667] INFO in action_factory: Registered basic actions: tap, wait
[2025-09-01 21:19:36,668] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-09-01 21:19:36,668] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,669] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,669] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-09-01 21:19:36,669] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,669] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,669] INFO in action_factory: Registered action handler for 'multiStep'
[2025-09-01 21:19:36,670] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-09-01 21:19:36,670] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,670] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,670] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-09-01 21:19:36,670] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,671] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,671] INFO in action_factory: Registered action handler for 'swipe'
[2025-09-01 21:19:36,671] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,671] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,671] INFO in action_factory: Registered action handler for 'getParam'
[2025-09-01 21:19:36,672] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,672] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,672] INFO in action_factory: Registered action handler for 'wait'
[2025-09-01 21:19:36,672] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,673] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,673] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-09-01 21:19:36,673] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,673] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,673] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-09-01 21:19:36,674] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,674] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,674] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-09-01 21:19:36,675] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,675] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,675] INFO in action_factory: Registered action handler for 'text'
[2025-09-01 21:19:36,676] INFO in action_factory: Special case: Registering tap_if_text_exists_action.py as 'tapIfTextExists'
[2025-09-01 21:19:36,676] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,676] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,676] INFO in action_factory: Registered action handler for 'tapIfTextExists'
[2025-09-01 21:19:36,677] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,677] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,677] INFO in action_factory: Registered action handler for 'waitTill'
[2025-09-01 21:19:36,677] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,678] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,678] INFO in action_factory: Registered action handler for 'hookAction'
[2025-09-01 21:19:36,678] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,678] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,679] INFO in action_factory: Registered action handler for 'inputText'
[2025-09-01 21:19:36,679] INFO in global_values_db: Android migration mode - skipping global values database initialization
[2025-09-01 21:19:36,680] INFO in global_values_db: Using global values from config.py
[2025-09-01 21:19:36,680] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-09-01 21:19:36,682] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,682] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,682] INFO in action_factory: Registered action handler for 'setParam'
[2025-09-01 21:19:36,683] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-09-01 21:19:36,683] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,683] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,683] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-09-01 21:19:36,684] INFO in action_factory: Special case: Registering android_functions_action.py as 'androidFunctions'
[2025-09-01 21:19:36,684] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,684] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,684] INFO in action_factory: Registered action handler for 'androidFunctions'
[2025-09-01 21:19:36,685] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,685] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,685] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-09-01 21:19:36,686] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,686] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,686] INFO in action_factory: Registered action handler for 'clickImage'
[2025-09-01 21:19:36,686] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,687] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,687] INFO in action_factory: Registered action handler for 'tap'
[2025-09-01 21:19:36,687] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,687] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,687] INFO in action_factory: Registered action handler for 'clearText'
[2025-09-01 21:19:36,688] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-09-01 21:19:36,688] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,688] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,688] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-09-01 21:19:36,689] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-09-01 21:19:36,689] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,689] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,689] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-09-01 21:19:36,689] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,689] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,689] INFO in action_factory: Registered action handler for 'hideKeyboard'
[2025-09-01 21:19:36,691] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,691] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,691] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-09-01 21:19:36,692] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-09-01 21:19:36,692] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,692] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,692] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-09-01 21:19:36,693] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,693] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,693] INFO in action_factory: Registered action handler for 'launchApp'
[2025-09-01 21:19:36,693] INFO in action_factory: Special case: Registering if_then_steps_action.py as 'ifThenSteps'
[2025-09-01 21:19:36,694] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,694] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,694] INFO in action_factory: Registered action handler for 'ifThenSteps'
[2025-09-01 21:19:36,694] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-09-01 21:19:36,694] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,694] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,694] INFO in action_factory: Registered action handler for 'info'
[2025-09-01 21:19:36,695] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,695] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,695] INFO in action_factory: Registered action handler for 'waitElement'
[2025-09-01 21:19:36,696] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,696] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,696] INFO in action_factory: Registered action handler for 'compareValue'
[2025-09-01 21:19:36,696] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,696] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,696] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-09-01 21:19:36,697] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-09-01 21:19:36,697] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,697] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,697] INFO in action_factory: Registered action handler for 'exists'
[2025-09-01 21:19:36,697] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,698] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,698] INFO in action_factory: Registered action handler for 'clickElement'
[2025-09-01 21:19:36,698] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,698] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,698] INFO in action_factory: Registered action handler for 'randomData'
[2025-09-01 21:19:36,699] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,699] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,699] INFO in action_factory: Registered action handler for 'getValue'
[2025-09-01 21:19:36,699] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,699] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,699] INFO in action_factory: Registered action handler for 'restartApp'
[2025-09-01 21:19:36,700] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-09-01 21:19:36,700] INFO in base_action: Enhanced element finder initialized
[2025-09-01 21:19:36,700] INFO in step_retry_manager: Step Retry Manager initialized with configuration
[2025-09-01 21:19:36,700] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-09-01 21:19:36,700] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'tapIfTextExists', 'waitTill', 'hookAction', 'inputText', 'setParam', 'repeatSteps', 'androidFunctions', 'swipeTillVisible', 'clickImage', 'clearText', 'takeScreenshot', 'tapIfLocatorExists', 'hideKeyboard', 'tapAndType', 'tapOnText', 'launchApp', 'ifThenSteps', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'restartApp', 'doubleTap']
[2025-09-01 21:19:36,700] INFO in action_factory: Handler for 'tap': TapAction
[2025-09-01 21:19:36,700] INFO in action_factory: Handler for 'wait': WaitAction
[2025-09-01 21:19:36,700] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-09-01 21:19:36,700] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-09-01 21:19:36,700] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-09-01 21:19:36,700] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-09-01 21:19:36,700] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-09-01 21:19:36,700] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'text': TextAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'tapIfTextExists': TapIfTextExistsAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'inputText': InputTextAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'androidFunctions': AndroidFunctionsAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'clearText': ClearTextAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'hideKeyboard': HideKeyboardAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'ifThenSteps': IfThenStepsAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'info': InfoAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-09-01 21:19:36,701] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-09-01 21:19:36,702] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-09-01 21:19:36,702] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-09-01 21:19:36,702] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-09-01 21:19:36,702] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-09-01 21:19:36,702] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-09-01 21:19:36,702] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-09-01 21:19:36,702] INFO in appium_device_controller: 🔄 Enhanced Android Airtest connection for device: PJTCI7EMSSONYPU8
[2025-09-01 21:19:36,739] INFO in appium_device_controller: 📱 Available ADB devices: ['PJTCI7EMSSONYPU8']
[2025-09-01 21:19:36,739] INFO in appium_device_controller: 🔄 Connection attempt 1/5: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-09-01 21:19:36,739] WARNING in appium_device_controller: ❌ Connection attempt 1 failed: 'No devices added.'
[2025-09-01 21:19:37,741] INFO in appium_device_controller: 🔄 Connection attempt 2/5: Android:///PJTCI7EMSSONYPU8
[2025-09-01 21:19:37,742] WARNING in appium_device_controller: ❌ Connection attempt 2 failed: 'No devices added.'
[2025-09-01 21:19:38,745] INFO in appium_device_controller: 🔄 Connection attempt 3/5: android://PJTCI7EMSSONYPU8
[2025-09-01 21:19:38,745] WARNING in appium_device_controller: ❌ Connection attempt 3 failed: 'No devices added.'
[2025-09-01 21:19:39,749] INFO in appium_device_controller: 🔄 Connection attempt 4/5: android://127.0.0.1:5037/PJTCI7EMSSONYPU8
[2025-09-01 21:19:39,749] WARNING in appium_device_controller: ❌ Connection attempt 4 failed: 'No devices added.'
[2025-09-01 21:19:40,750] INFO in appium_device_controller: 🔄 Connection attempt 5/5: Android:///PJTCI7EMSSONYPU8?cap_method=JAVACAP
[2025-09-01 21:19:40,750] WARNING in appium_device_controller: ❌ Connection attempt 5 failed: 'No devices added.'
[2025-09-01 21:19:41,751] WARNING in appium_device_controller: 🔄 All Airtest connection attempts failed, setting up Appium fallback
[2025-09-01 21:19:41,751] INFO in appium_device_controller: ✅ Appium image fallback initialized successfully
[2025-09-01 21:19:41,826] DEBUG in appium_device_controller: Session health check passed - current activity: .MainActivity
[2025-09-01 21:19:41,827] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/my_custom_temp_ios/screenshots/screenshot_20250901_211941.png (save_debug=False)
[2025-09-01 21:19:41,827] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-09-01 21:19:42,389] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250901_211941.png
[2025-09-01 21:19:42,405] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:42] "POST /api/device/connect HTTP/1.1" 200 -
[2025-09-01 21:19:43,414] INFO in appium_device_controller: 📸 Visual operation detected - forcing screenshot (context: device_connection, action: None)
[2025-09-01 21:19:43,487] DEBUG in appium_device_controller: Session health check passed - current activity: .MainActivity
[2025-09-01 21:19:43,487] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-09-01 21:19:43,487] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-09-01 21:19:43,957] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-09-01 21:19:43,957] INFO in _internal: 127.0.0.1 - - [01/Sep/2025 21:19:43] "GET /screenshot?deviceId=PJTCI7EMSSONYPU8&clientSessionId=client_1756725556722_tmqvva70l_1756723133028_gqciqtwds&t=1756725583411 HTTP/1.1" 200 -
[2025-09-01 21:19:45,548] INFO in player: Executing action: {'type': 'tap', 'timestamp': 1756725570381, 'locator_type': 'xpath', 'locator_value': '//android.widget.Button[contains(@text,"Remove1")]', 'timeout': 10, 'interval': 0.5, 'method': 'locator', 'action_id': '19idcit4gd'}
[2025-09-01 21:19:45,565] INFO in app: Using directories from config_android.py:
[2025-09-01 21:19:45,565] INFO in app:   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/android_data/test_cases
[2025-09-01 21:19:45,565] INFO in app:   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/android_data/reference_images
[2025-09-01 21:19:45,565] INFO in app:   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
[2025-09-01 21:19:56,424] INFO in timeout_performance_monitor: Action completed: tap - Duration: 10.77s, Efficiency: 1.08, Found: False, Retries: 0
[2025-09-01 21:19:56,424] INFO in appium_device_controller: Tapping on element with xpath='//android.widget.Button[contains(@text,"Remove1")]' (timeout=10s, interval=0.5s)
[2025-09-01 21:19:56,425] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//android.widget.Button[contains(@text,"Remove1")]'
[2025-09-01 21:20:33,399] WARNING in appium_device_controller: Element not clickable within timeout: xpath='//android.widget.Button[contains(@text,"Remove1")]'
[2025-09-01 21:20:33,399] INFO in appium_device_controller: Trying to find element even if not clickable: xpath='//android.widget.Button[contains(@text,"Remove1")]'
[2025-09-01 21:20:34,428] WARNING in appium_device_controller: Could not find or tap on element: Message: 
Stacktrace:
NoSuchElementError: An element could not be located on the page using the given search parameters.
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:86:11)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/appium-uiautomator2-driver/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElement (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/appium-uiautomator2-driver/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:75:12)
[2025-09-01 21:20:34,429] INFO in appium_device_controller: 🔄 Starting enhanced retry mechanism for: xpath='//android.widget.Button[contains(@text,"Remove1")]'
[2025-09-01 21:20:34,429] INFO in appium_device_controller: 🎯 Attempting coordinate-based fallback
[2025-09-01 21:20:36,955] DEBUG in appium_device_controller: Coordinate fallback failed: Message: 
Stacktrace:
NoSuchElementError: An element could not be located on the page using the given search parameters.
    at AndroidUiautomator2Driver.findElOrEls (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/find.ts:86:11)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at AndroidUiautomator2Driver.findElOrElsWithProcessing (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/appium-uiautomator2-driver/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)
    at AndroidUiautomator2Driver.findElement (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/appium-uiautomator2-driver/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:75:12)
[2025-09-01 21:20:36,955] ERROR in appium_device_controller: ❌ All retry strategies failed for: xpath='//android.widget.Button[contains(@text,"Remove1")]'
[2025-09-01 21:20:38,045] DEBUG in appium_device_controller: Session health check passed - current activity: .MainActivity
[2025-09-01 21:20:38,045] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/my_custom_temp_ios/screenshots/screenshot_20250901_212038.png (save_debug=False)
[2025-09-01 21:20:38,045] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-09-01 21:20:38,620] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250901_212038.png
/usr/local/Cellar/python@3.13/3.13.7/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/resource_tracker.py:324: UserWarning: resource_tracker: There appear to be 1 leaked semaphore objects to clean up at shutdown: {'/mp-g81y6wx_'}
  warnings.warn(
