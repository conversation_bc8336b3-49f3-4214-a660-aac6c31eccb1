"""Configuration file for OCR optimization settings"""

import os
from typing import Dict, Any

class OCRConfig:
    """
    Configuration class for OCR optimization settings
    """
    
    def __init__(self):
        """Initialize OCR configuration with default values"""
        
        # Tesseract configurations for different scenarios
        self.tesseract_configs = {
            'fast': {
                'config': '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz ',
                'description': 'Fast processing with character whitelist',
                'use_case': 'General text detection with good speed'
            },
            'accurate': {
                'config': '--oem 1 --psm 3',
                'description': 'Most accurate OCR engine with automatic page segmentation',
                'use_case': 'High accuracy requirements, slower processing'
            },
            'sparse': {
                'config': '--oem 3 --psm 11',
                'description': 'Sparse text detection',
                'use_case': 'Scattered text elements, UI components'
            },
            'single_word': {
                'config': '--oem 3 --psm 8',
                'description': 'Single word detection',
                'use_case': 'Button labels, single words'
            },
            'single_char': {
                'config': '--oem 3 --psm 10',
                'description': 'Single character detection',
                'use_case': 'Icons with single characters'
            },
            'digits_only': {
                'config': '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789',
                'description': 'Digits only detection',
                'use_case': 'Numbers, prices, quantities'
            },
            'alpha_only': {
                'config': '--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',
                'description': 'Alphabetic characters only',
                'use_case': 'Text without numbers or symbols'
            },
            'vertical_text': {
                'config': '--oem 3 --psm 5',
                'description': 'Vertical text detection',
                'use_case': 'Vertical text layouts'
            },
            'uniform_block': {
                'config': '--oem 3 --psm 6',
                'description': 'Uniform block of text',
                'use_case': 'Paragraphs, consistent text blocks'
            }
        }
        
        # Preprocessing configurations
        self.preprocessing_configs = {
            'light': {
                'description': 'Basic preprocessing with Otsu thresholding',
                'operations': ['grayscale', 'otsu_threshold'],
                'performance': 'fastest',
                'quality': 'basic'
            },
            'medium': {
                'description': 'Enhanced preprocessing with adaptive thresholding',
                'operations': ['grayscale', 'gaussian_blur', 'adaptive_threshold', 'morphology'],
                'performance': 'balanced',
                'quality': 'good'
            },
            'heavy': {
                'description': 'Aggressive preprocessing for difficult images',
                'operations': ['bilateral_filter', 'clahe', 'adaptive_threshold', 'morphology', 'dilation', 'erosion'],
                'performance': 'slower',
                'quality': 'best'
            }
        }
        
        # Performance settings
        self.performance_settings = {
            'cache_enabled': True,
            'cache_size': 100,
            'max_workers': 4,
            'confidence_threshold': 30,
            'overlap_threshold': 0.5,
            'similarity_threshold_default': 0.7
        }
        
        # Image preprocessing parameters
        self.preprocessing_params = {
            'gaussian_blur_kernel': (3, 3),
            'bilateral_filter_d': 9,
            'bilateral_filter_sigma_color': 75,
            'bilateral_filter_sigma_space': 75,
            'clahe_clip_limit': 2.0,
            'clahe_tile_grid_size': (8, 8),
            'adaptive_threshold_max_value': 255,
            'adaptive_threshold_block_size': 11,
            'adaptive_threshold_c': 2,
            'morph_kernel_size': (3, 3),
            'dilation_iterations': 1,
            'erosion_iterations': 1
        }
        
        # Auto-detection rules
        self.auto_detection_rules = {
            'digits_only': {
                'condition': 'text.isdigit()',
                'config': 'digits_only'
            },
            'alpha_only': {
                'condition': 'text.isalpha()',
                'config': 'alpha_only'
            },
            'single_char': {
                'condition': 'len(text) == 1',
                'config': 'single_char'
            },
            'single_word': {
                'condition': '" " not in text and len(text) <= 15',
                'config': 'single_word'
            },
            'sparse_text': {
                'condition': 'len(text.split()) <= 3',
                'config': 'sparse'
            },
            'default': {
                'condition': 'True',
                'config': 'fast'
            }
        }
        
        # Load environment-specific overrides
        self._load_environment_overrides()
    
    def _load_environment_overrides(self):
        """Load configuration overrides from environment variables"""
        
        # Cache settings
        if os.getenv('OCR_CACHE_ENABLED'):
            self.performance_settings['cache_enabled'] = os.getenv('OCR_CACHE_ENABLED').lower() == 'true'
        
        if os.getenv('OCR_CACHE_SIZE'):
            try:
                self.performance_settings['cache_size'] = int(os.getenv('OCR_CACHE_SIZE'))
            except ValueError:
                pass
        
        # Performance settings
        if os.getenv('OCR_MAX_WORKERS'):
            try:
                self.performance_settings['max_workers'] = int(os.getenv('OCR_MAX_WORKERS'))
            except ValueError:
                pass
        
        if os.getenv('OCR_CONFIDENCE_THRESHOLD'):
            try:
                self.performance_settings['confidence_threshold'] = int(os.getenv('OCR_CONFIDENCE_THRESHOLD'))
            except ValueError:
                pass
        
        if os.getenv('OCR_SIMILARITY_THRESHOLD'):
            try:
                self.performance_settings['similarity_threshold_default'] = float(os.getenv('OCR_SIMILARITY_THRESHOLD'))
            except ValueError:
                pass
        
        # Tesseract path override
        if os.getenv('TESSERACT_CMD'):
            from utils.resource_manager import resource_manager as _rm
            pytesseract = _rm.pytesseract
            if pytesseract is not None:
                pytesseract.pytesseract.tesseract_cmd = os.getenv('TESSERACT_CMD')
    
    def get_config(self, config_type: str) -> str:
        """
        Get Tesseract configuration string for a specific type
        
        Args:
            config_type: Type of configuration to retrieve
            
        Returns:
            Tesseract configuration string
        """
        return self.tesseract_configs.get(config_type, self.tesseract_configs['fast'])['config']
    
    def get_preprocessing_config(self, level: str) -> Dict[str, Any]:
        """
        Get preprocessing configuration for a specific level
        
        Args:
            level: Preprocessing level ('light', 'medium', 'heavy')
            
        Returns:
            Preprocessing configuration dictionary
        """
        return self.preprocessing_configs.get(level, self.preprocessing_configs['medium'])
    
    def get_performance_setting(self, setting: str) -> Any:
        """
        Get a performance setting value
        
        Args:
            setting: Setting name
            
        Returns:
            Setting value
        """
        return self.performance_settings.get(setting)
    
    def get_preprocessing_param(self, param: str) -> Any:
        """
        Get a preprocessing parameter value
        
        Args:
            param: Parameter name
            
        Returns:
            Parameter value
        """
        return self.preprocessing_params.get(param)
    
    def list_available_configs(self) -> Dict[str, str]:
        """
        List all available Tesseract configurations
        
        Returns:
            Dictionary mapping config names to descriptions
        """
        return {name: config['description'] for name, config in self.tesseract_configs.items()}
    
    def list_preprocessing_levels(self) -> Dict[str, str]:
        """
        List all available preprocessing levels
        
        Returns:
            Dictionary mapping level names to descriptions
        """
        return {name: config['description'] for name, config in self.preprocessing_configs.items()}
    
    def update_config(self, config_type: str, config_string: str, description: str = ""):
        """
        Update or add a Tesseract configuration
        
        Args:
            config_type: Configuration type name
            config_string: Tesseract configuration string
            description: Description of the configuration
        """
        self.tesseract_configs[config_type] = {
            'config': config_string,
            'description': description,
            'use_case': 'Custom configuration'
        }
    
    def update_performance_setting(self, setting: str, value: Any):
        """
        Update a performance setting
        
        Args:
            setting: Setting name
            value: New value
        """
        self.performance_settings[setting] = value
    
    def update_preprocessing_param(self, param: str, value: Any):
        """
        Update a preprocessing parameter
        
        Args:
            param: Parameter name
            value: New value
        """
        self.preprocessing_params[param] = value
    
    def export_config(self) -> Dict[str, Any]:
        """
        Export current configuration as a dictionary
        
        Returns:
            Complete configuration dictionary
        """
        return {
            'tesseract_configs': self.tesseract_configs,
            'preprocessing_configs': self.preprocessing_configs,
            'performance_settings': self.performance_settings,
            'preprocessing_params': self.preprocessing_params,
            'auto_detection_rules': self.auto_detection_rules
        }
    
    def import_config(self, config_dict: Dict[str, Any]):
        """
        Import configuration from a dictionary
        
        Args:
            config_dict: Configuration dictionary to import
        """
        if 'tesseract_configs' in config_dict:
            self.tesseract_configs.update(config_dict['tesseract_configs'])
        
        if 'preprocessing_configs' in config_dict:
            self.preprocessing_configs.update(config_dict['preprocessing_configs'])
        
        if 'performance_settings' in config_dict:
            self.performance_settings.update(config_dict['performance_settings'])
        
        if 'preprocessing_params' in config_dict:
            self.preprocessing_params.update(config_dict['preprocessing_params'])
        
        if 'auto_detection_rules' in config_dict:
            self.auto_detection_rules.update(config_dict['auto_detection_rules'])


# Global configuration instance
ocr_config = OCRConfig()


# Convenience functions
def get_tesseract_config(config_type: str = 'fast') -> str:
    """
    Get Tesseract configuration string
    
    Args:
        config_type: Configuration type
        
    Returns:
        Tesseract configuration string
    """
    return ocr_config.get_config(config_type)


def get_preprocessing_level(level: str = 'medium') -> Dict[str, Any]:
    """
    Get preprocessing configuration
    
    Args:
        level: Preprocessing level
        
    Returns:
        Preprocessing configuration
    """
    return ocr_config.get_preprocessing_config(level)


def set_tesseract_path(path: str):
    """
    Set Tesseract executable path
    
    Args:
        path: Path to Tesseract executable
    """
    import pytesseract
    pytesseract.pytesseract.tesseract_cmd = path


def enable_cache(enabled: bool = True):
    """
    Enable or disable OCR result caching
    
    Args:
        enabled: Whether to enable caching
    """
    ocr_config.update_performance_setting('cache_enabled', enabled)


def set_cache_size(size: int):
    """
    Set OCR cache size
    
    Args:
        size: Maximum number of cached results
    """
    ocr_config.update_performance_setting('cache_size', size)


def set_max_workers(workers: int):
    """
    Set maximum number of worker threads for batch processing
    
    Args:
        workers: Number of worker threads
    """
    ocr_config.update_performance_setting('max_workers', workers)