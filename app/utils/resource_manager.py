"""
Lightweight lazy import manager for heavy optional modules.

Usage:
    from utils.resource_manager import resource_manager
    cv2 = resource_manager.opencv
    pyt = resource_manager.pytesseract

This avoids importing heavy libraries at module import time and returns None
when a library isn't available so callers can gracefully degrade.
"""
from __future__ import annotations

from typing import Any, Optional


class ResourceManager:
    def __init__(self) -> None:
        self._opencv: Optional[Any] = None
        self._pytesseract: Optional[Any] = None
        self._easyocr: Optional[Any] = None
        self._airtest: Optional[Any] = None

    @property
    def opencv(self):
        if self._opencv is None:
            try:
                import cv2  # type: ignore
                self._opencv = cv2
            except Exception:
                self._opencv = None
        return self._opencv

    @property
    def pytesseract(self):
        if self._pytesseract is None:
            try:
                import pytesseract  # type: ignore
                self._pytesseract = pytesseract
            except Exception:
                self._pytesseract = None
        return self._pytesseract

    @property
    def easyocr(self):
        if self._easyocr is None:
            try:
                import easyocr  # type: ignore
                self._easyocr = easyocr
            except Exception:
                self._easyocr = None
        return self._easyocr

    @property
    def airtest(self):
        if self._airtest is None:
            try:
                import airtest  # type: ignore
                self._airtest = airtest
            except Exception:
                self._airtest = None
        return self._airtest


# Singleton instance
resource_manager = ResourceManager()

