#!/usr/bin/env python
"""
Mobile App Automation Tool - Entry Point
"""
import os
import sys
import signal
import subprocess
import time
import logging
import argparse
import glob
import shutil
from importlib import import_module

# Add app directory to path
app_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app')
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

# Project root directory to path for config
root_dir = os.path.dirname(os.path.abspath(__file__))
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

# Import after adding app directory to path
sys.path.append(app_dir)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# CLI launchers default to filesystem-based directories unless explicitly overridden
os.environ.setdefault('SKIP_DIRECTORY_DB', 'true')

# Set JAVA_HOME environment variable if not set correctly
try:
    # Check if JAVA_HOME is set and valid
    java_home = os.environ.get('JAVA_HOME')
    if not java_home or not os.path.exists(java_home):
        # Try to find Java home using /usr/libexec/java_home
        if sys.platform == 'darwin':  # macOS
            try:
                java_home_process = subprocess.run(
                    ['/usr/libexec/java_home'],
                    check=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                java_home = java_home_process.stdout.strip()
                if java_home and os.path.exists(java_home):
                    os.environ['JAVA_HOME'] = java_home
                    logger.info(f"Set JAVA_HOME to {java_home}")
                    print(f"Set JAVA_HOME to {java_home}")
            except Exception as e:
                logger.warning(f"Failed to set JAVA_HOME: {e}")
                print(f"Warning: Failed to set JAVA_HOME: {e}")
except Exception as e:
    logger.warning(f"Error checking/setting JAVA_HOME: {e}")
    print(f"Warning: Error checking/setting JAVA_HOME: {e}")


def _load_dynamic_port_initializer():
    """Return initialize_platform_ports from the project utils package."""
    try:
        from utils.dynamic_port_init import initialize_platform_ports  # type: ignore
        return initialize_platform_ports
    except ModuleNotFoundError as import_err:
        if import_err.name != 'utils.dynamic_port_init':
            raise

        project_root = os.path.dirname(os.path.abspath(__file__))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

        module = import_module('utils.dynamic_port_init')
        return module.initialize_platform_ports

# Function to clean screenshots directory
def clean_screenshots_directory():
    """Delete all screenshots in the app/static/screenshots directory"""
    screenshots_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'static', 'screenshots')
    if os.path.exists(screenshots_dir):
        logger.info(f"Cleaning screenshots directory: {screenshots_dir}")
        try:
            # Delete all files in the directory
            files = glob.glob(os.path.join(screenshots_dir, '*'))
            for file in files:
                if os.path.isfile(file):
                    os.remove(file)
                    logger.debug(f"Deleted screenshot: {file}")
            logger.info(f"Cleaned {len(files)} screenshots")
        except Exception as e:
            logger.error(f"Error cleaning screenshots directory: {e}")
    else:
        logger.warning(f"Screenshots directory not found: {screenshots_dir}")
        # Create the directory if it doesn't exist
        try:
            os.makedirs(screenshots_dir, exist_ok=True)
            logger.info(f"Created screenshots directory: {screenshots_dir}")
        except Exception as e:
            logger.error(f"Error creating screenshots directory: {e}")

# Function to start Appium server
def start_appium_server(port=4723):
    """Start Appium server with inspector plugin and CORS enabled"""
    logger.info(f"Starting Appium server on port {port} with inspector plugin and CORS enabled...")
    try:
        # Build the command
        cmd = [
            'appium',
            '--port', str(port),
            '--use-plugins=inspector',
            '--allow-cors'
        ]

        # Start Appium as a background process
        appium_log = open('appium_server.log', 'w')
        process = subprocess.Popen(
            cmd,
            stdout=appium_log,
            stderr=appium_log,
            text=True
        )

        # Wait a bit to ensure Appium has started
        time.sleep(5)

        # Check if the process is still running
        if process.poll() is None:
            logger.info(f"Appium server started successfully on port {port}")
            return process
        else:
            logger.error(f"Appium server failed to start. Check appium_server.log for details.")
            return None
    except Exception as e:
        logger.error(f"Error starting Appium server: {e}")
        return None

# Function to start iproxy
def start_iproxy(device_port=8100, local_port=8100):
    """Start iproxy to forward iOS device port to local port"""
    logger.info(f"Starting iproxy to forward device port {device_port} to local port {local_port}...")
    try:
        # Build the command
        cmd = [
            'iproxy',
            str(local_port),
            str(device_port)
        ]

        # Start iproxy as a background process
        iproxy_log = open('iproxy.log', 'w')
        process = subprocess.Popen(
            cmd,
            stdout=iproxy_log,
            stderr=iproxy_log,
            text=True
        )

        # Wait a bit to ensure iproxy has started
        time.sleep(2)

        # Check if the process is still running
        if process.poll() is None:
            logger.info(f"iproxy started successfully (device port {device_port} -> local port {local_port})")
            return process
        else:
            logger.error(f"iproxy failed to start. Check iproxy.log for details.")
            return None
    except Exception as e:
        logger.error(f"Error starting iproxy: {e}")
        return None

# Function to kill existing processes
def kill_existing_processes(force_kill=False):
    """
    Kill existing Appium and iproxy processes

    Args:
        force_kill (bool): If True, kill processes regardless of port configuration.
                          If False, only kill if using default ports.
    """
    if not force_kill:
        logger.info("Skipping process termination when using custom ports for multi-instance support")
        return

    logger.info("Killing any existing Appium and iproxy processes...")
    try:
        if sys.platform == 'win32':
            # Windows
            subprocess.run(['taskkill', '/F', '/IM', 'node.exe'],
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
        else:
            # macOS/Linux
            subprocess.run(['pkill', '-f', 'appium'],
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
            subprocess.run(['pkill', '-f', 'iproxy'],
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
        time.sleep(2)  # Wait for processes to terminate
        logger.info("Existing processes terminated")
    except Exception as e:
        logger.error(f"Error killing processes: {e}")

# Signal handler for graceful shutdown
def signal_handler(sig, frame):
    print("\nShutting down and cleaning up...")
    if 'device_controller' in globals() and device_controller:
        device_controller.shutdown()
    sys.exit(0)

def kill_processes_on_ports(ports, preserve_on_custom_ports=True, custom_flask_port=None):
    """Kill processes on specified ports with platform awareness"""
    default_flask_port = 8080
    default_appium_port = 4723
    default_wda_port = 8100
    
    for port in ports:
        # Skip killing processes if using custom ports (for multi-instance support)
        if preserve_on_custom_ports and custom_flask_port:
            if (port == default_flask_port and custom_flask_port != default_flask_port) or \
               (port == default_appium_port and custom_flask_port != default_flask_port) or \
               (port == default_wda_port and custom_flask_port != default_flask_port):
                print(f"Preserving processes on port {port} for multi-instance support")
                continue
        
        try:
            # Find processes using the port
            result = subprocess.run(['lsof', '-ti', f':{port}'], 
                                  capture_output=True, text=True, check=False)
            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    if pid:
                        print(f"Killing process {pid} on port {port}")
                        subprocess.run(['kill', '-9', pid], check=False)
        except Exception as e:
            print(f"Error killing processes on port {port}: {e}")

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Mobile App Automation Tool')
    parser.add_argument('--flask-port', type=int, help='Flask server port (if not specified, will be dynamically allocated)')
    parser.add_argument('--appium-port', type=int, help='Appium server port (if not specified, will be dynamically allocated)')
    parser.add_argument('--wda-port', type=int, help='WebDriverAgent port (if not specified, will be dynamically allocated)')
    parser.add_argument('--use-static-ports', action='store_true', help='Use static ports instead of dynamic allocation')
    args = parser.parse_args()
    
    # Initialize dynamic ports if not using static ports
    if not args.use_static_ports:
        try:
            initialize_platform_ports = _load_dynamic_port_initializer()
            custom_ports = {}
            if args.flask_port:
                custom_ports['flask'] = args.flask_port
            if args.appium_port:
                custom_ports['appium'] = args.appium_port
            if args.wda_port:
                custom_ports['wda'] = args.wda_port
                
            allocated_ports = initialize_platform_ports('ios', custom_ports if custom_ports else None)
            
            # Override args with allocated ports
            args.flask_port = allocated_ports['flask']
            args.appium_port = allocated_ports['appium']
            args.wda_port = allocated_ports['wda']
            
            logger.info(f"Using dynamically allocated ports: Flask={args.flask_port}, Appium={args.appium_port}, WDA={args.wda_port}")
        except Exception as e:
            logger.warning(f"Dynamic port allocation failed: {e}. Using fallback ports.")
            args.flask_port = args.flask_port or 8080
            args.appium_port = args.appium_port or 4723
            args.wda_port = args.wda_port or 8200
    else:
        # Use provided ports or defaults for static mode
        args.flask_port = args.flask_port or 8080
        args.appium_port = args.appium_port or 4723
        args.wda_port = args.wda_port or 8200

    # Set global port configuration
    import config
    config.FLASK_PORT = args.flask_port
    config.APPIUM_PORT = args.appium_port
    # Note: Don't override WDA_PORT for multi-device support - let device controller read device-specific ports
    # config.WDA_PORT = args.wda_port

    # Update APPIUM_CONFIG with the new port
    config.APPIUM_CONFIG['PORT'] = args.appium_port

    # Update FlaskConfig with the new port
    config.FlaskConfig.PORT = args.flask_port

    # iOS app should NOT create instance-specific database files according to DATABASE_MIGRATION_GUIDE.md
    # Clear any existing instance-specific environment variables to prevent unauthorized database creation
    import os
    os.environ.pop('INSTANCE_DB_SUFFIX', None)
    os.environ.pop('INSTANCE_PORT', None)

    # Set flag to prevent unauthorized database file creation
    os.environ['IOS_MIGRATION_MODE'] = 'true'

    logger.info("iOS app configured to use only the 5 specified database files in app/data/")

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Use intelligent port management instead of aggressive cleanup
    logger.info(f"Using intelligent port management for iOS platform...")

    # Import the new port manager
    sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))
    try:
        from utils.port_manager import get_port_manager, ensure_platform_ports

        # Ensure iOS platform ports are available
        custom_ports = {
            'flask': args.flask_port,
            'appium': args.appium_port,
            'wda': args.wda_port
        }

        try:
            service_config = ensure_platform_ports('ios', custom_ports)
            logger.info(f"Successfully allocated iOS ports: Flask={service_config.flask_port}, Appium={service_config.appium_port}, WDA={service_config.wda_port}")
        except RuntimeError as e:
            logger.error(f"Failed to allocate iOS ports: {e}")
            # Fall back to basic cleanup
            using_default_ports = (args.flask_port == 8082 and args.appium_port == 4723 and args.wda_port == 8200)
            if using_default_ports:
                logger.info("Falling back to default port cleanup")
                kill_existing_processes(force_kill=True)
            else:
                logger.info("Falling back to custom port cleanup")
                kill_existing_processes(force_kill=False)

    except ImportError:
        logger.warning("Port manager not available, falling back to basic cleanup")
        using_default_ports = (args.flask_port == 8082 and args.appium_port == 4723 and args.wda_port == 8200)
        if using_default_ports:
            logger.info("Using default ports - killing existing processes to avoid conflicts")
            kill_existing_processes(force_kill=True)
        else:
            logger.info(f"Using custom ports (Flask: {args.flask_port}, Appium: {args.appium_port}, WDA: {args.wda_port}) - preserving existing processes for multi-instance support")
            kill_existing_processes(force_kill=False)

    # Now import the app after setting configuration
    # Import using the app directory that's already in sys.path
    import sys
    import os

    # Temporarily add the app directory to the path for imports
    app_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app')
    if app_path not in sys.path:
        sys.path.insert(0, app_path)

    # Import the Flask app and device controller
    import app as flask_app
    from utils.appium_device_controller import AppiumDeviceController
    app = flask_app.app

    # Initialize the device controller with the configured Appium port
    # Note: Don't pass wda_port here for multi-device support - let controller read device-specific ports from wda_ports.txt
    device_controller = AppiumDeviceController(appium_port=args.appium_port)
    # Make it available to the app
    flask_app.device_controller = device_controller

    print(f"Starting Mobile App Automation Tool...")
    print(f"Configuration:")
    print(f"  - Flask server port: {args.flask_port}")
    print(f"  - Appium server port: {args.appium_port}")
    print(f"  - WebDriverAgent port: {args.wda_port}")
    print(f"Open your web browser and navigate to: http://localhost:{args.flask_port}")
    # Disable reloader to prevent state loss during development testing
    SECURE = os.getenv('SECURE_BUILD', 'False').lower() == 'true'
    host = '127.0.0.1' if SECURE else '0.0.0.0'
    debug = False if SECURE else True
    app.run(debug=debug, use_reloader=False, host=host, port=args.flask_port)
