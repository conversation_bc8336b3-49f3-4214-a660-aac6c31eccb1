[package]
name = "python-packaging"
version = "0.16.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
license = "MIT OR Apache-2.0"
description = "Python packaging primitives implemented in Rust"
readme = "README.md"
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
keywords = ["python"]

[dependencies]
anyhow = "1.0.92"
base64 = { version = "0.22.1", optional = true }
byteorder = "1.5.0"
encoding_rs = "0.8.35"
itertools = "0.13.0"
mailparse = "0.15.0"
once_cell = "1.20.2"
regex = "1.11.1"
serde = { version = "1.0.214", features = ["derive"], optional = true }
sha2 = { version = "0.10.8", optional = true }
simple-file-manifest = "0.11.0"
spdx = "0.10.6"
time = { version = "0.3.36", optional = true }
walkdir = "2.5.0"

[dependencies.python-packed-resources]
version = "0.12.0-pre"
path = "../python-packed-resources"

[dependencies.zip]
version = "2.2.0"
optional = true
default-features = false
features = ["deflate"]

[dev-dependencies]
tempfile = "3.13.0"

# We make `wheel` support optional because it has dependencies that we don't
# want to bloat the dependency tree with.
[features]
default = ["wheel"]
serialization = ["serde"]
spdx-text = ["spdx/text"]
wheel = ["base64", "sha2", "time", "zip"]
