# About

This crate contains data structures and functions related to Python
packaging. It's goal is to support interacting with Python resources
and performing packaging-like mechanisms without the involvement of
Python.

This crate is developed as part of the
[PyOxidizer](https://gregoryszorc.com/docs/pyoxidizer/stable/pyoxidizer.html)
project. But it can be used without PyOxidizer.

# Getting Started

This crate defines a Rust library. There's nothing special about the library
and it can be operated on like a typical Rust crate:

    $ cargo build
    $ cargo test
    $ cargo doc
