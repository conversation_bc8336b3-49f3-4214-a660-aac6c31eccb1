[package]
name = "tugger-snapcraft"
version = "0.15.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
license = "MPL-2.0"
description = "Snapcraft packaging primitives"
keywords = ["package", "snapcraft", "tugger"]
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
readme = "README.md"

[dependencies]
anyhow = "1.0.92"
duct = "0.13.7"
log = "0.4.22"
remove_dir_all = "0.8.4"
serde_yaml = "0.9.34"
serde = { version = "1.0.214", features = ["derive"] }
simple-file-manifest = "0.11.0"

[dependencies.tugger-common]
version = "0.10.0-pre"
path = "../tugger-common"
