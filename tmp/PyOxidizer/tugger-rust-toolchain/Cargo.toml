[package]
name = "tugger-rust-toolchain"
version = "0.13.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
license = "MPL-2.0"
description = "Discover, download, and use Rust toolchains"
keywords = ["rustup"]
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
readme = "README.md"

[dependencies]
anyhow = "1.0.92"
flate2 = "1.0.34"
fs2 = "0.4.3"
hex = "0.4.3"
log = "0.4.22"
once_cell = "1.20.2"
pgp = "0.14.0"
sha2 = "0.10.8"
simple-file-manifest = "0.11.0"
tar = "0.4.43"
toml = "0.8.19"
xz2 = { version = "0.1.7", features = ["static"] }
zstd = "0.13.2"

[dependencies.tugger-common]
version = "0.10.0-pre"
path = "../tugger-common"

[dev-dependencies]
dirs = "5.0.1"
tempfile = "3.13.0"
