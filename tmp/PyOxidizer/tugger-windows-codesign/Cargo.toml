[package]
name = "tugger-windows-codesign"
version = "0.10.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
license = "MPL-2.0"
description = "Code signing on Windows platforms"
keywords = ["tugger", "windows", "codesign", "authenticode", "signtool"]
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
readme = "README.md"

[dependencies]
anyhow = "1.0.92"
duct = "0.13.7"
log = "0.4.22"
p12 = "0.6.3"
rcgen = "0.10.0"
time = "0.3.36"
yasna = "0.5.2"

[dependencies.tugger-common]
version = "0.10.0-pre"
path = "../tugger-common"

[dependencies.tugger-windows]
version = "0.10.0-pre"
path = "../tugger-windows"

[dev-dependencies]
der-parser = "9.0.0"
x509-parser = "0.16.0"
