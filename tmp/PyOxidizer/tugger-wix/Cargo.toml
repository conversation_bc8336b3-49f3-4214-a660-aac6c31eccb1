[package]
name = "tugger-wix"
version = "0.16.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
license = "MPL-2.0"
description = "Interfaces to the WiX Toolset to produce Windows installers"
keywords = ["package", "tugger", "windows", "wix", "wix-toolset"]
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
readme = "README.md"
build = "build.rs"

[dependencies]
anyhow = "1.0.92"
duct = "0.13.7"
log = "0.4.22"
once_cell = "1.20.2"
simple-file-manifest = "0.11.0"
url = "2.5.2"
uuid = { version = "1.11.0", features = ["v4", "v5"] }
xml-rs = "0.8.22"

[dependencies.tugger-common]
version = "0.10.0-pre"
path = "../tugger-common"

[dependencies.tugger-windows-codesign]
version = "0.10.0-pre"
path = "../tugger-windows-codesign"

[dependencies.tugger-windows]
version = "0.10.0-pre"
path = "../tugger-windows"

[dev-dependencies]
tempfile = "3.13.0"

[target.'cfg(windows)'.dev-dependencies]
msi = "0.8.0"
