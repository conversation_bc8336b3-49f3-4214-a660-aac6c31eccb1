# About

This crate defines and implements a data format for storing resources
useful to the execution of a Python interpreter. We call this data format
*Python packed resources.* See the crate's documentation for more.

This crate is developed primarily for
[PyOxidizer](https://gregoryszorc.com/docs/pyoxidizer/stable/pyoxidizer.html).
But it can be used outside the PyOxidizer project.

# Getting Started

This crate defines a Rust library. There's nothing special about the library
and it can be operated on like a typical Rust crate:

    $ cargo build
    $ cargo test
    $ cargo doc
