[package]
name = "release"
version = "0.1.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
description = "Performs a release of PyOxidizer"
license = "MPL-2.0"
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"

[package.metadata.release]
# Internal package is never released.
release = false

[dependencies]
anyhow = "1.0.92"
cargo-lock = "10.0.1"
cargo_toml = "0.20.5"
clap = "4.5.20"
duct = "0.13.7"
# We don't need network functionality.
git2 = { version = "0.19.0", default-features = false }
once_cell = "1.20.2"
pulldown-cmark = "0.12.2"
rustdoc-types = "0.32.2"
semver = "1.0.23"
serde_json = "1.0.132"
tempfile = "3.13.0"
toml = "0.8.19"
url = "2.5.2"
