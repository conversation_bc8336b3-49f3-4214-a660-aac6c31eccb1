[package]
name = "tugger"
version = "0.18.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
license = "MPL-2.0"
description = "Package and distribute applications"
keywords = ["package"]
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
readme = "README.md"
build = "build.rs"

[dependencies]
anyhow = "1.0.92"
apple-bundles = "0.20.0"
atty = "0.2.14"
codemap = "0.1.3"
codemap-diagnostic = "0.1.2"
console = "0.15.8"
dialoguer = "0.11.0"
linked-hash-map = "0.5.6"
log = "0.4.22"
plist = "1.7.0"
simple-file-manifest = "0.11.0"
starlark = "0.3.2"
tar = "0.4.43"
tempfile = "3.13.0"
time = "0.3.36"
walkdir = "2.5.0"

[dependencies.python-packaging]
version = "0.16.0-pre"
path = "../python-packaging"

[dependencies.starlark-dialect-build-targets]
version = "0.8.0-pre"
path = "../starlark-dialect-build-targets"

[dependencies.tugger-apple]
version = "0.8.0-pre"
path = "../tugger-apple"

[dependencies.tugger-code-signing]
version = "0.10.0-pre"
path = "../tugger-code-signing"

[dependencies.tugger-common]
version = "0.10.0-pre"
path = "../tugger-common"

[dependencies.tugger-snapcraft]
version = "0.15.0-pre"
path = "../tugger-snapcraft"

[dependencies.tugger-windows]
version = "0.10.0-pre"
path = "../tugger-windows"

[dependencies.tugger-wix]
version = "0.16.0-pre"
path = "../tugger-wix"

[dev-dependencies]
apple-codesign = "0.28.0"
chrono = "0.4.38"
tempfile = "3.13.0"
x509-certificate = "0.24.0"

[dev-dependencies.tugger-windows-codesign]
version = "0.10.0-pre"
path = "../tugger-windows-codesign"
