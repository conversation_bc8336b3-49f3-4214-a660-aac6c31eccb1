.. _pyoxy_history:

===============
Project History
===============

Blog Posts
==========

* `Announcing the PyOxy Python Runner <https://gregoryszorc.com/blog/2022/05/10/announcing-the-pyoxy-python-runner/>`_ (2022-05-10)

Changelog
=========

0.3.0
-----

Not yet released.

0.2.0
-----

Released on 2022-05-30.

* Official release artifacts now contain Python 3.8 and 3.10 variants.
  Previously, only Python 3.9 was provided.
* The Sphinx documentation now contains extensive documentation for the
  Python interpreter configuration structs and enums. The content is derived
  from the canonical Rust source code. This should make it easier to
  understand the fields in YAML configurations without having to consult
  Rust crate docs.
* Release artifacts are now ``.tar.gz`` files and contain a ``COPYING``
  file with licensing annotations.
* The release mechanism for PyOxy is now streamlined, hopefully enabling
  faster releases going forward.

0.1.0
-----

Released 2022-05-10.