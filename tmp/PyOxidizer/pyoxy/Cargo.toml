[package]
name = "pyoxy"
version = "0.3.0-pre"
authors = ["<PERSON> <<EMAIL>>"]
license = "MPL-2.0"
description = "Self-contained Python distribution and application runner."
readme = "README.md"
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
keywords = ["python"]
edition = "2021"
build = "build.rs"

[package.metadata.release]
# PyOxy is only published as a binary, not to crates.io.
release = false

[dependencies]
anyhow = "1.0.92"
clap = "4.5.20"
serde = { version = "1.0.214", features = ["derive"] }
serde_yaml = "0.9.34"

[dependencies.pyembed]
version = "0.25.0-pre"
path = "../pyembed"
default-features = false
features = ["serialization", "zipimport"]

[dev-dependencies]
assert_cmd = "2.0.16"
glob = "0.3.1"
libtest-mimic = "0.8.1"
predicates = "3.1.2"
tempfile = "3.13.0"

[[test]]
name = "python"
harness = false

[[test]]
name = "yaml"
harness = false
