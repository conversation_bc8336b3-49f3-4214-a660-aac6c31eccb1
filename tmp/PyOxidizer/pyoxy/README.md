# PyOxy

PyOxy is a Python runner. PyOxy enables you to run Python interpreters with far
more control and features than `python`.

PyOxy is part of the
[PyOxidizer](https://github.com/indygreg/PyOxidizer.git) project. However,
PyOxy is intended to be useful as a standalone project/tool and is developed as
such. However, it leverages a lot of functionality developed for the PyOxidizer
project and its canonical source repository is the aforementioned PyOxidizer
repository.
