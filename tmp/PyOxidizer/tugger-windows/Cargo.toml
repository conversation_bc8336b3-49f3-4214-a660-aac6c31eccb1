[package]
name = "tugger-windows"
version = "0.10.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
license = "MPL-2.0"
description = "Collection of packaging primitives specific to Windows"
keywords = ["tugger", "windows"]
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
readme = "README.md"

[dependencies]
anyhow = "1.0.92"
glob = "0.3.1"
once_cell = "1.20.2"

[target.'cfg(windows)'.dependencies]
duct = "0.13.7"
find-winsdk = "0.2.0"
semver = "1.0.23"
winapi = { version = "0.3.9", features = ["combaseapi", "knownfolders", "shlobj", "shtypes"] }

[dependencies.tugger-common]
version = "0.10.0-pre"
path = "../tugger-common"
