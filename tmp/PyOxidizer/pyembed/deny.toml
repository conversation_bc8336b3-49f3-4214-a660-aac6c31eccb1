# pyembed is shipped with applications built with PyOxidizer. We want
# pyembed to only use permissive licenses so a proprietary application
# can be distributed with PyOxidizer.
[licenses]
allow = [
    "0BSD",
    "Apache-2.0",
    "Apache-2.0 WITH LLVM-exception",
    "BSD-3-Clause",
    "BSL-1.0",
    "CC0-1.0",
    "MIT",
    "MPL-2.0",
    "Python-2.0",
    "Unicode-DFS-2016",
    "Zlib",
]

[bans]
# We want to keep the dependency tree as slim as possible. Let's
# warn when we're shipping multiple versions of the same crate.
multiple-versions = "warn"

