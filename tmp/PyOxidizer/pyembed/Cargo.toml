[package]
name = "pyembed"
# Remember to keep this version string in sync with the PYEMBED_CRATE_VERSION
# constant in pyoxidizer's environment.rs. The release automation should do
# this automatically.
version = "0.25.0-pre"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
license = "Python-2.0 OR MPL-2.0"
description = "Embed a Python interpreter"
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
build = "build.rs"
readme = "README.md"

[package.metadata.release]
pre-release-hook = ["/bin/bash", "-c", "cd $WORKSPACE_ROOT; cargo run --bin release -- synchronize-generated-files"]

[[package.metadata.release.pre-release-replacements]]
file = "../pyoxidizer/src/environment.rs"
search = "const PYEMBED_CRATE_VERSION: &str = .*;"
replace = "const PYEMBED_CRATE_VERSION: &str = \"{{version}}\";"
exactly = 1

[dependencies]
# Update documentation in lib.rs when new dependencies are added.
anyhow = "1.0.92"
dunce = "1.0.5"
jemalloc-sys = { version = "0.5.4", optional = true }
libc = "0.2.161"
once_cell = "1.20.2"
serde = { version = "1.0", features = ["derive"], optional = true }

[dependencies.snmalloc-sys]
version = "0.3.6"
features = ["build_cc"]
optional = true

[dependencies.libmimalloc-sys]
version = "0.1.39"
features = [
    "extended",
    "local_dynamic_tls",
    "override",
]
optional = true

[dependencies.python-oxidized-importer]
version = "0.9.0-pre"
path = "../python-oxidized-importer"
default-features = false

[dependencies.pyo3]
version = "0.18.3"
default-features = false
features = ["macros"]

[dependencies.python-packaging]
version = "0.16.0-pre"
path = "../python-packaging"
default-features = false

[build-dependencies]
pyo3-build-config = { version = "0.18.3", features = ["resolve-config"] }

[dev-dependencies]
pathdiff = "0.2.2"
rusty-fork = "0.3.0"

[dev-dependencies.python-packed-resources]
version = "0.12.0-pre"
path = "../python-packed-resources"

[features]
default = ["zipimport"]
allocator-jemalloc = ["jemalloc-sys"]
allocator-mimalloc = ["libmimalloc-sys"]
allocator-snmalloc = ["snmalloc-sys"]
serialization = ["serde", "python-packaging/serialization"]
zipimport = ["python-oxidized-importer/zipimport"]
