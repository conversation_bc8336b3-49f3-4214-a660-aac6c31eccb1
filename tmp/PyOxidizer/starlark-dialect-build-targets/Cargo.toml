[package]
name = "starlark-dialect-build-targets"
version = "0.8.0"
authors = ["<PERSON> <<EMAIL>>"]
license = "MPL-2.0"
readme = "README.md"
description = "A Starlark dialect providing simple build system primitives"
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
edition = "2021"

[dependencies]
anyhow = "1.0.92"
codemap = "0.1.3"
codemap-diagnostic = "0.1.2"
linked-hash-map = "0.5.6"
log = "0.4.22"
path-dedot = "3.1.1"
starlark = "0.3.2"
