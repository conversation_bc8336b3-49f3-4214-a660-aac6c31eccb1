[package]
name = "tugger-code-signing"
version = "0.10.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
license = "MPL-2.0"
description = "Cross-platform code signing functionality"
keywords = ["tugger"]
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
readme = "README.md"

[dependencies]
anyhow = "1.0.92"
apple-bundles = "0.20.0"
apple-codesign = "0.28.0"
bcder = "0.7.4"
cryptographic-message-syntax = "0.27.0"
goblin = "0.9.2"
log = "0.4.22"
p12 = "0.6.3"
reqwest = { version = "0.12.9", default-features = false, features = ["blocking", "rustls-tls"] }
simple-file-manifest = "0.11.0"
tempfile = "3.13.0"
thiserror = "1.0.67"
x509-certificate = "0.24.0"
yasna = "0.5.2"

[dependencies.tugger-windows-codesign]
version = "0.10.0-pre"
path = "../tugger-windows-codesign"
