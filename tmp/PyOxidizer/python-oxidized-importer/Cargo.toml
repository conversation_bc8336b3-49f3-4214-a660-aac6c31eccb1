[package]
name = "python-oxidized-importer"
version = "0.9.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
license = "Python-2.0 OR MPL-2.0"
description = "A Rust-powered Python importer"
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
readme = "README.md"

[[package.metadata.release.pre-release-replacements]]
file = "docs/oxidized_importer_history.rst"
search = "\\(Not yet released\\)"
replace = "Released {{date}}"
exactly = 1

[[package.metadata.release.pre-release-replacmenets]]
file = "docs/oxidized_importer_history.rst"
search = "Next\n----"
replace = "{{version}}\n------"
exactly = 1

[[package.metadata.release.pre-release-replacements]]
file = "docs/oxidized_importer_history.rst"
search = "\\.\\. next-release"
replace = ".. next-release\n\nNext\n----\n\n(Not yet released)"
exactly = 1

[lib]
name = "oxidized_importer"
crate-type = ["cdylib", "lib"]

[dependencies]
anyhow = "1.0.92"
memmap2 = "0.9.5"
once_cell = "1.20.2"
simple-file-manifest = "0.11.0"

[dependencies.python-packed-resources]
version = "0.12.0-pre"
path = "../python-packed-resources"

[dependencies.python-packaging]
version = "0.16.0-pre"
path = "../python-packaging"
default-features = false

[dependencies.pyo3]
version = "0.18.3"
features = ["macros"]

[dependencies.zip]
version = "2.2.0"
optional = true
default-features = false
features = ["deflate"]

[target.'cfg(windows)'.dependencies]
memory-module-sys = "0.3.0"
winapi = { version = "0.3.9", features = ["libloaderapi", "memoryapi", "minwindef"] }

[features]
default = ["zipimport"]

# Build the crate in Python extension module mode. This will make linking
# correct so the resulting library can be loaded as a Python extension
# module.
extension-module = ["pyo3/extension-module"]

# Enable support for importing from zip files.
zipimport = ["zip"]
