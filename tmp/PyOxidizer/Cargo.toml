[workspace]
members = [
    'pyembed-bench',
    'pyembed',
    'pyoxidizer',
    'pyoxy',
    'python-oxidized-importer',
    'python-packaging',
    'python-packed-resources',
    'release',
    'starlark-dialect-build-targets',
    'tugger',
    'tugger-apple',
    'tugger-binary-analysis',
    'tugger-code-signing',
    'tugger-common',
    'tugger-rust-toolchain',
    'tugger-snapcraft',
    'tugger-windows',
    'tugger-windows-codesign',
    'tugger-wix',
]
resolver = "2"

[workspace.metadata.release]
allow-branch = ["main"]
push-remote = "origin"
pre-release-commit-message = "workspace: perform releases"
tag-message = "{{crate_name}}: version {{version}}"
tag-name = "{{crate_name}}/{{version}}"
tag = true
dependent-version = "fix"
