on:
  push:
    branches-ignore:
      - 'ci-test'
    tags-ignore:
      - '**'
  pull_request:
  schedule:
    - cron: '13 15 * * *'
  workflow_dispatch:
jobs:
  sphinx:
    strategy:
      fail-fast: false
      matrix:
        dir:
          - python-oxidized-importer/docs
          - pyembed/docs
          - pyoxy/docs
          - tugger/docs
          - docs
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: Run Sphinx
        run: |
          python3.12 -m pip install Sphinx==8.1.3
          make -C ${{ matrix.dir }} html
