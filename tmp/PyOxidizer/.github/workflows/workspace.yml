on:
  push:
    branches-ignore:
      - 'ci-test'
    tags-ignore:
      - '**'
  pull_request:
  schedule:
    - cron: '13 15 * * *'
  workflow_dispatch:
jobs:
  build-and-test:
    strategy:
      fail-fast: false
      matrix:
        rust_toolchain:
          - 'stable'
          - 'beta'
          - 'nightly'
          # Remember to update MINIMUM_RUST_VERSION in pyoxidizer/src/environment.rs
          # and the `Installing Rust` documentation when this changes.
          - '1.81.0'
        target:
          - os: 'ubuntu-22.04'
            triple: 'x86_64-unknown-linux-gnu'
          - os: 'macos-12'
            triple: 'x86_64-apple-darwin'
          - os: 'windows-2022'
            triple: 'x86_64-pc-windows-msvc'
    continue-on-error: ${{ matrix.rust_toolchain == 'nightly' }}
    runs-on: ${{ matrix.target.os }}
    env:
      IN_CI: '1'
      AWS_REGION: us-west-2
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      CARGO_INCREMENTAL: '0'
      CARGO_TERM_COLOR: always
      SCCACHE_BUCKET: 'pyoxidizer-sccache'
      SCCACHE_S3_USE_SSL: '1'
      # Prevent sccache server from stopping due to inactivity.
      SCCACHE_IDLE_TIMEOUT: '0'
    steps:
      - name: Install Linux system packages
        if: runner.os == 'Linux'
        run: |
          sudo apt-get install -y libyaml-dev snapcraft

      - uses: actions/checkout@v4
        with:
          # Needed by build.rs, which needs to walk the Git history to find the
          # root commit. Without this, a shallow clone (depth=1) is performed.
          fetch-depth: 0

      - uses: ./.github/actions/rust-bootstrap
        with:
          rust_toolchain: ${{ matrix.rust_toolchain }}
          rust_target: ${{ matrix.target.triple }}

      - name: Build Workspace
        env:
          RUSTC_WRAPPER: sccache
        run: |
          rustc --version
          cargo build --workspace --exclude pyembed --exclude pyembed-bench --exclude pyoxy --exclude python-oxidized-importer
          cargo nextest run --no-run --workspace --exclude pyembed --exclude pyembed-bench --exclude pyoxy --exclude python-oxidized-importer

      - name: Test Workspace
        env:
          RUSTC_WRAPPER: sccache
        run: |
          cargo nextest run --no-fail-fast --workspace --exclude pyembed --exclude pyembed-bench --exclude pyoxy --exclude python-oxidized-importer

      - uses: actions-rs/clippy@master
        if: ${{ matrix.rust_toolchain == 'stable' || matrix.rust_toolchain == 'beta' }}
        env:
          RUSTC_WRAPPER: sccache
        with:
          args: --workspace

      - name: Stop sccache
        run: |
          sccache --stop-server
