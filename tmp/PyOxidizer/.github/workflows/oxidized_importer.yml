on:
  push:
    branches-ignore:
      - 'ci-test'
    tags-ignore:
      - '**'
  pull_request:
  schedule:
    - cron: '13 15 * * *'
  workflow_dispatch:
jobs:
  wheel_linux:
    strategy:
      fail-fast: false
      matrix:
        py:
          - arch: 'aarch64'
            build: 'cp38-manylinux_aarch64'
          - arch: 'i686'
            build: 'cp38-manylinux_i686'
          - arch: 'x86_64'
            build: 'cp38-manylinux_x86_64'

          - arch: 'aarch64'
            build: 'cp39-manylinux_aarch64'
          - arch: 'i686'
            build: 'cp39-manylinux_i686'
          - arch: 'x86_64'
            build: 'cp39-manylinux_x86_64'

          - arch: 'aarch64'
            build: 'cp310-manylinux_aarch64'
          - arch: 'i686'
            build: 'cp310-manylinux_i686'
          - arch: 'x86_64'
            build: 'cp310-manylinux_x86_64'
    runs-on: ubuntu-22.04
    env:
      CIBW_ENVIRONMENT: 'PATH="$PATH:$HOME/.cargo/bin"'
      CIBW_BEFORE_BUILD: 'ci/install-rust-linux.sh'
      CIBW_ARCHS: ${{ matrix.py.arch }}
      CIBW_BUILD: ${{ matrix.py.build }}
      CIBW_BUILD_VERBOSITY: '1'
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          profile: minimal
      - uses: actions/setup-python@v5
        with:
          python-version: 3.9
      - name: Run cibuildwheel
        run: |
          if [ "${CIBW_ARCHS}" = "aarch64" ]; then
              sudo apt update
              sudo apt install -y qemu binfmt-support qemu-user-static
          fi

          python3 -m pip install --upgrade pip==22.0.3
          python3 -m pip install cibuildwheel==2.3.1
          python3 -m cibuildwheel --output-dir wheelhouse .

      - name: Upload wheels
        uses: actions/upload-artifact@v3
        with:
          name: wheels
          path: |
            wheelhouse

  wheel_macos:
    strategy:
      fail-fast: false
      matrix:
        py:
          - build: 'cp38-macosx_x86_64'
          - build: 'cp39-macosx_x86_64'
          - build: 'cp310-macosx_x86_64'
    runs-on: macos-12
    env:
      CIBW_BUILD: ${{ matrix.py.build }}
      CIBW_BUILD_VERBOSITY: '1'
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          profile: minimal
      - uses: actions/setup-python@v5
        with:
          python-version: 3.9
      - name: Run cibuildwheel
        run: |
          python3 -m pip install --upgrade pip==22.0.3
          python3 -m pip install cibuildwheel==2.3.1
          python3 -m cibuildwheel --output-dir wheelhouse .

      - name: Upload wheels
        uses: actions/upload-artifact@v3
        with:
          name: wheels
          path: |
            wheelhouse

  wheel_windows:
    strategy:
      fail-fast: false
      matrix:
        py:
          - build: 'cp38-win_amd64'
          - build: 'cp39-win_amd64'
          - build: 'cp310-win_amd64'
    runs-on: windows-2022
    env:
      CIBW_BUILD_VERBOSITY: '1'
      CIBW_BUILD: ${{ matrix.py.build }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          profile: minimal
      - uses: actions/setup-python@v5
        with:
          python-version: 3.9
      - name: Run cibuildwheel
        run: |
          python -m pip install --upgrade pip==22.0.3
          python -m pip install cibuildwheel==2.3.1
          python -m cibuildwheel --output-dir wheelhouse .

      - name: Upload wheels
        uses: actions/upload-artifact@v3
        with:
          name: wheels
          path: |
            wheelhouse
