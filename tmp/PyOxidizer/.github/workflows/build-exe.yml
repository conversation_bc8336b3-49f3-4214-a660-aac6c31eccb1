name: Build a binary

on:
  workflow_call:
    inputs:
      bin:
        description: Name of binary to build
        required: true
        type: string
      extra_build_args_linux:
        description: Extra argumnets to pass to `cargo build` on Linux
        required: false
        type: string
      extra_build_args_macos:
        description: Extra arguments to pass to `cargo build` on Windows
        required: false
        type: string
      extra_build_args_windows:
        description: Extra arguments to pass to `cargo build` on macOS
        required: false
        type: string
    secrets:
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        target:
          - os: 'ubuntu-22.04'
            triple: 'aarch64-unknown-linux-musl'
            cross: true
          - os: 'ubuntu-22.04'
            triple: 'x86_64-unknown-linux-musl'
          - os: 'macos-12'
            triple: 'aarch64-apple-darwin'
          - os: 'macos-12'
            triple: 'x86_64-apple-darwin'
          - os: 'windows-2022'
            triple: 'i686-pc-windows-msvc'
          - os: 'windows-2022'
            triple: 'x86_64-pc-windows-msvc'
    runs-on: ${{ matrix.target.os }}
    env:
      IN_CI: '1'
      AWS_REGION: us-west-2
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      SCCACHE_BUCKET: 'pyoxidizer-sccache'
      SCCACHE_S3_USE_SSL: '1'
      # Prevent sccache server from stopping due to inactivity.
      SCCACHE_IDLE_TIMEOUT: '0'
      RUSTC_WRAPPER: sccache
      CARGO_INCREMENTAL: '0'
      # PYOXIDIZER_BUILD_FORCE_GIT_SOURCE is set to prevent pyoxidizer's build.rs from
      # writing the filesystem path to the Git checkout in the built binary. With the
      # filesystem path embedded in the binary, pyoxidizer will probe for that path
      # at run-time and will attempt to use it for the location of the pyembed crate.
      # With the filesystem path not set, pyoxidizer will use the canonical github.com
      # repository for any Git references.
      PYOXIDIZER_BUILD_FORCE_GIT_SOURCE: '1'

      # This forces the lzma-sys crate to link liblzma statically. Ideally we'd do
      # this with a crate feature. But the xz2 crate didn't expose that feature at the
      # time this workaround was introduced.
      LZMA_API_STATIC: '1'
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: ./.github/actions/rust-bootstrap
        with:
          rust_toolchain: stable
          rust_target: ${{ matrix.target.triple }}

      - uses: actions-rs/install@v0.1
        if: matrix.target.cross
        with:
          crate: cross
          version: latest

      - name: Build
        shell: bash
        run: |
          if [ "${{ matrix.target.triple }}" = "aarch64-apple-darwin" ]; then
            export MACOSX_DEPLOYMENT_TARGET=11.0
          elif [ "${{ matrix.target.triple }}" = "x86_64-apple-darwin" ]; then
            export MACOSX_DEPLOYMENT_TARGET=10.9
          fi

          if [ -n "${{ matrix.target.cross }}" ]; then
            CARGO=cross
          else
            CARGO=cargo
          fi

          case "${{ matrix.target.triple }}" in
            *linux*)
              EXTRA_BUILD_ARGS=${{ inputs.extra_build_args_linux }}
              ;;
            *apple-darwin*)
              EXTRA_BUILD_ARGS=${{ inputs.extra_build_args_macos }}
              ;;
            *windows*)
              EXTRA_BUILD_ARGS=${{ inputs.extra_build_args_windows }}
              ;;
            *)
              ;;
          esac

          $CARGO build --release --bin ${{ inputs.bin }} --target ${{ matrix.target.triple }} ${EXTRA_BUILD_ARGS}

          mkdir upload
          cp target/${{ matrix.target.triple }}/release/${{ inputs.bin }}* upload/
          sccache --stop-server

      - name: Upload (non-Windows)
        if: runner.os != 'Windows'
        uses: actions/upload-artifact@v3
        with:
          name: exe-${{ inputs.bin }}-${{ matrix.target.triple }}
          path: upload/${{ inputs.bin }}

      - name: Upload (Windows)
        if: runner.os == 'Windows'
        uses: actions/upload-artifact@v3
        with:
          name: exe-${{ inputs.bin }}-${{ matrix.target.triple }}
          path: upload/${{ inputs.bin }}.exe

  macos-universal:
    needs:
      - build
    runs-on: 'macos-12'
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: ./.github/actions/install-just

      - name: Download aarch64 executable
        uses: actions/download-artifact@v3
        with:
          name: exe-${{ inputs.bin }}-aarch64-apple-darwin
          path: ${{ inputs.bin }}-aarch64

      - name: Download x86-64 executable
        uses: actions/download-artifact@v3
        with:
          name: exe-${{ inputs.bin }}-x86_64-apple-darwin
          path: ${{ inputs.bin }}-x86-64

      - name: Produce Universal Binary
        run: |
          just actions-macos-universal ${{ inputs.bin }}

      - name: Upload Universal Executable
        uses: actions/upload-artifact@v3
        with:
          name: exe-${{ inputs.bin }}-macos-universal
          path: uploads/${{ inputs.bin }}
