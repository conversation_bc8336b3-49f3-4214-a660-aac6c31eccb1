# For the spot tests, we purposefully perform these on separate jobs
# because we don't want a Python or Rust install from the build environment
# to impact the testing.

on:
  push:
    branches-ignore:
      - 'ci-test'
    tags-ignore:
      - '**'
  pull_request:
  schedule:
    - cron: '13 15 * * *'
  workflow_dispatch:
jobs:
  pyoxidizer-exes:
    uses: ./.github/workflows/build-exe.yml
    with:
      bin: pyoxidizer
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

  pyoxidizer-linux-x86_64-bin-check:
    needs:
      - pyoxidizer-exes
    runs-on: 'ubuntu-22.04'
    steps:
      - name: Install musl compiler
        run: |
          sudo apt install -y --no-install-recommends musl-tools

      - name: Download Executable
        uses: actions/download-artifact@v3
        with:
          name: exe-pyoxidizer-x86_64-unknown-linux-musl

      - name: Spot Test (Native Build)
        run: |
          chmod +x pyoxidizer
          ./pyoxidizer init-config-file myapp
          ./pyoxidizer build --path myapp

      - name: Spot Test (musl Cross)
        run: |
          ./pyoxidizer build --path myapp --target-triple x86_64-unknown-linux-musl

  pyoxidizer-macos-universal-exe-check:
    needs:
      - pyoxidizer-exes
    runs-on: 'macos-12'
    steps:
      - name: Download macOS Executable
        uses: actions/download-artifact@v3
        with:
          name: exe-pyoxidizer-macos-universal
          path: macos-universal

      - name: Spot Test (Native Build)
        run: |
          find . -type f
          mv macos-universal/pyoxidizer .
          chmod +x pyoxidizer
          ./pyoxidizer init-config-file myapp
          ./pyoxidizer build --path myapp

      - name: Spot Test (aarch64 Cross Build)
        run: |
          ./pyoxidizer build --path myapp --target-triple aarch64-apple-darwin

  pyoxidizer-windows-exe-check:
    strategy:
      fail-fast: false
      matrix:
        config:
          - host_triple: 'i686-pc-windows-msvc'
            target_triple: 'x86_64-pc-windows-msvc'
          - host_triple: 'x86_64-pc-windows-msvc'
            target_triple: 'i686-pc-windows-msvc'
    needs:
      - pyoxidizer-exes
    runs-on: 'windows-2022'
    steps:
      - name: Download Windows Executable
        uses: actions/download-artifact@v3
        with:
          name: exe-pyoxidizer-i686-pc-windows-msvc
          path: i686-pc-windows-msvc/

      - name: Download Windows Executable
        uses: actions/download-artifact@v3
        with:
          name: exe-pyoxidizer-x86_64-pc-windows-msvc
          path: x86_64-pc-windows-msvc/

      - name: Spot Test (Native Build)
        run: |
          ${{ matrix.config.host_triple }}/pyoxidizer.exe init-config-file myapp
          ${{ matrix.config.host_triple }}/pyoxidizer.exe build --path myapp

      - name: Spot Test (Cross-Compile Build)
        run: |
          ${{ matrix.config.host_triple }}/pyoxidizer.exe build --path myapp --target-triple ${{ matrix.config.target_triple }}

  pyoxidizer-windows-installer:
    needs:
      - pyoxidizer-exes
    runs-on: 'windows-2022'
    env:
      IN_CI: '1'
    steps:
      - uses: actions/checkout@v4

      - name: Download Windows Executable
        uses: actions/download-artifact@v3
        with:
          name: exe-pyoxidizer-i686-pc-windows-msvc
          path: dist/i686-pc-windows-msvc/

      - name: Download Windows Executable
        uses: actions/download-artifact@v3
        with:
          name: exe-pyoxidizer-x86_64-pc-windows-msvc
          path: dist/x86_64-pc-windows-msvc/

      # We can use the built/downloaded pyoxidizer.exe to package itself. Nice.
      # Building the exe installer will build the MSI installers. 2 birds 1 stone.
      - name: Build Installers
        run: |
          dist/x86_64-pc-windows-msvc/pyoxidizer.exe build --var-env IN_CI IN_CI --release --target-triple x86_64-pc-windows-msvc exe_installer
          move build/x86_64-pc-windows-msvc/release/*/*.msi .
          move build/x86_64-pc-windows-msvc/release/*/*.exe .

      - name: Upload installers
        uses: actions/upload-artifact@v3
        with:
          name: pyoxidizer-windows_installers
          path: |
            *.exe
            *.msi
