on:
  push:
    branches-ignore:
      - 'ci-test'
    tags-ignore:
      - '**'
  pull_request:
  schedule:
    - cron: '13 15 * * *'
  workflow_dispatch:
jobs:
  test:
    strategy:
      fail-fast: false
      matrix:
        target:
          - os: 'ubuntu-22.04'
            triple: 'x86_64-unknown-linux-gnu'
          - os: 'macos-12'
            triple: 'x86_64-apple-darwin'
          - os: 'windows-2022'
            triple: 'i686-pc-windows-msvc'
          - os: 'windows-2022'
            triple: 'x86_64-pc-windows-msvc'
        python_version:
          - '3.8'
          - '3.9'
          - '3.10'

    runs-on: ${{ matrix.target.os }}
    env:
      IN_CI: '1'
      AWS_REGION: us-west-2
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      CARGO_INCREMENTAL: '0'
      CARGO_TERM_COLOR: always
      SCCACHE_BUCKET: 'pyoxidizer-sccache'
      SCCACHE_S3_USE_SSL: '1'
      # Prevent sccache server from stopping due to inactivity.
      SCCACHE_IDLE_TIMEOUT: '0'
    steps:
      - uses: actions/checkout@v4
        with:
          # Needed by build.rs, which needs to walk the Git history to find the
          # root commit. Without this, a shallow clone (depth=1) is performed.
          fetch-depth: 0

      - uses: ./.github/actions/rust-bootstrap
        with:
          rust_toolchain: stable
          rust_target: ${{ matrix.target.triple }}

      # To minimize potential for Python interference, we delay install Python
      # until we need it for python-oxidized-importer/pyembed.
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python_version }}

      - name: Test pyembed
        env:
          RUSTC_WRAPPER: sccache
        run: |
          python scripts/run-pyembed-tests.py pyembed

      - name: Test PyOxy
        # PyOxy not yet supported on Windows.
        if: runner.os != 'Windows'
        env:
          RUSTC_WRAPPER: sccache
        run: |
          python scripts/run-pyembed-tests.py pyoxy

      - name: Test python-oxidized-importer
        env:
          RUSTC_WRAPPER: sccache
        run: |
          python scripts/run-pyembed-tests.py python-oxidized-importer

      - name: Stop sccache
        run: |
          sccache --stop-server
