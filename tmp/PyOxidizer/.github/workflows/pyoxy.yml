on:
  push:
    branches-ignore:
      - 'ci-test'
    tags-ignore:
      - '**'
  pull_request:
  schedule:
    - cron: '13 15 * * *'
  workflow_dispatch:
jobs:
  pyoxidizer-linux:
    runs-on: 'ubuntu-22.04'
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/rust-bootstrap
        with:
          rust_toolchain: stable
          rust_target: x86_64-unknown-linux-musl

      - name: Build pyoxidizer Executable
        run: |
          cargo build --bin pyoxidizer --target x86_64-unknown-linux-musl
          # The binary is a bit large so strip it.
          strip target/x86_64-unknown-linux-musl/debug/pyoxidizer

      - name: Upload
        uses: actions/upload-artifact@v3
        with:
          name: pyoxidizer-exe-linux
          path: target/x86_64-unknown-linux-musl/debug/pyoxidizer

  pyoxidizer-macos:
    runs-on: 'macos-12'
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/rust-bootstrap
        with:
          rust_toolchain: stable
          rust_target: x86_64-apple-darwin

      - name: Build pyoxidizer Executable
        run: |
          cargo build --bin pyoxidizer
          # The binary is a bit large so strip it.
          strip target/debug/pyoxidizer

      - name: Upload
        uses: actions/upload-artifact@v3
        with:
          name: pyoxidizer-exe-macos
          path: target/debug/pyoxidizer

  build-linux:
    needs:
      - pyoxidizer-linux
    strategy:
      fail-fast: false
      matrix:
        python_version:
          - '3.8'
          - '3.9'
          - '3.10'
        target_triple:
          - 'x86_64-unknown-linux-gnu'
          # Not yet working due to rust config issues.
          # - 'x86_64-unknown-linux-musl'
    runs-on: 'ubuntu-22.04'
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/actions/rust-bootstrap
        with:
          rust_toolchain: stable
          rust_target: ${{ matrix.target_triple }}

      - name: Download PyOxidizer Executable
        uses: actions/download-artifact@v3
        with:
          name: pyoxidizer-exe-linux
          path: /usr/local/bin/

      - name: Restore Docker Image Cache
        id: cache-image
        uses: actions/cache@v2
        with:
          path: ~/image.tar
          key: ${{ runner.os }}-${{ hashFiles('ci/linux-portable-binary.Dockerfile') }}

      - name: Build Docker Image
        if: steps.cache-image.outputs.cache-hit != 'true'
        run: |
          cd ci
          docker build -f linux-portable-binary.Dockerfile -t pyoxidizer:build .
          docker save -o ~/image.tar pyoxidizer:build

      - name: Load Docker Image
        if: steps.cache-image.outputs.cache-hit == 'true'
        run: |
          docker load -i ~/image.tar

      - name: Build pyoxy Linux Executable
        run: |
          chmod +x /usr/local/bin/pyoxidizer
          just actions-build-pyoxy-linux ${{ matrix.target_triple }} ${{ matrix.python_version }}

      - name: Upload
        uses: actions/upload-artifact@v3
        with:
          name: exe-pyoxy-${{ matrix.target_triple }}-${{ matrix.python_version }}
          path: upload/*

  # This is nearly identical to build-exe.yml and could likely be consolidated
  # with some additional parameters to that workflow.
  build-macos:
    needs:
      - pyoxidizer-macos
    strategy:
      fail-fast: false
      matrix:
        python_version:
          - '3.8'
          - '3.9'
          - '3.10'
        target_triple:
          - 'aarch64-apple-darwin'
          - 'x86_64-apple-darwin'
    runs-on: 'macos-12'
    env:
      IN_CI: '1'
      AWS_REGION: us-west-2
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      SCCACHE_BUCKET: 'pyoxidizer-sccache'
      SCCACHE_S3_USE_SSL: '1'
      # Prevent sccache server from stopping due to inactivity.
      SCCACHE_IDLE_TIMEOUT: '0'
      CARGO_INCREMENTAL: '0'
      RUSTC_WRAPPER: sccache
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: ./.github/actions/rust-bootstrap
        with:
          rust_toolchain: stable
          rust_target: ${{ matrix.target_triple }}

      - name: Download PyOxidizer Executable
        uses: actions/download-artifact@v3
        with:
          name: pyoxidizer-exe-macos
          path: /usr/local/bin/

      - name: Build
        run: |
          chmod +x /usr/local/bin/pyoxidizer
          just actions-build-pyoxy-macos ${{ matrix.target_triple }} ${{ matrix.python_version }}

      - name: Upload
        uses: actions/upload-artifact@v3
        with:
          name: exe-pyoxy-${{ matrix.target_triple }}-${{ matrix.python_version }}
          path: upload/*

  macos-universal:
    needs:
      - build-macos
    strategy:
      fail-fast: false
      matrix:
        python_version:
          - '3.8'
          - '3.9'
          - '3.10'
    runs-on: 'macos-12'
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: ./.github/actions/install-just

      - name: Download aarch64 executable
        uses: actions/download-artifact@v3
        with:
          name: exe-pyoxy-aarch64-apple-darwin-${{ matrix.python_version }}
          path: pyoxy-aarch64

      - name: Download x86-64 executable
        uses: actions/download-artifact@v3
        with:
          name: exe-pyoxy-x86_64-apple-darwin-${{ matrix.python_version }}
          path: pyoxy-x86-64

      - name: Produce Universal Binary
        run: |
          just actions-macos-universal pyoxy

      - name: Upload Universal Executable
        uses: actions/upload-artifact@v3
        with:
          name: exe-pyoxy-macos-universal-${{ matrix.python_version }}
          path: uploads/*
