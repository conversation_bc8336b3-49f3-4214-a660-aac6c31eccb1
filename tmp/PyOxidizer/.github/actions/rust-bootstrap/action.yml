name: Bootstrap Rust Building
description: Configures the system environment for building Rust
inputs:
  rust_toolchain:
    description: rustup toolchain to install
    default: stable
    required: false
  rust_target:
    description: rust target triple to install
    required: true
runs:
  using: composite
  steps:
    - uses: ./.github/actions/install-just

    - uses: dtolnay/rust-toolchain@v1
      with:
        toolchain: ${{ inputs.rust_toolchain }}
        targets: ${{ inputs.rust_target }}
        components: clippy

    - uses: taiki-e/install-action@nextest

    - name: Bootstrap Environment (Linux)
      if: runner.os == 'Linux'
      shell: bash
      run: |
        just actions-bootstrap-rust-linux

    - name: Bootstrap Environment (macOS)
      if: runner.os == 'macOS'
      shell: bash
      run: |
        just actions-bootstrap-rust-macos

    - name: Bootstrap Environment (Windows)
      if: runner.os == 'Windows'
      shell: pwsh
      run: |
        just actions-bootstrap-rust-windows

    - name: Start sccache
      shell: bash
      run: |
        sccache --start-server
