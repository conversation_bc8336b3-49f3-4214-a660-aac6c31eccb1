.. py:currentmodule:: starlark_pyoxidizer

====================
``PythonExecutable``
====================

.. py:class:: PythonExecutable

    The :py:class:`PythonExecutable` type represents an executable file containing
    the Python interpreter, Python resources to make available to the interpreter,
    and a default run-time configuration for that interpreter.

    Instances are constructed from :py:class:`PythonDistribution` instances
    using :py:meth:`PythonDistribution.to_python_executable`.

    .. py:attribute:: licenses_filename

        (``str``)

        The filename to use / write for an auto-generated report of software
        component licensing relevant to the built executable.

        The file will contain a bill-of-materials of all the known software
        components in the built binary. This includes information about the
        Python distribution, extension modules and libraries used by the
        Python distribution, 3rd party Python packages, and Rust crates.

        Each component is annotated with licensing information, including the
        license text, if available.

        All content in the file is best effort.

        If ``None``, no file will be written.

        Default: ``COPYING.txt``

    .. py:attribute:: packed_resources_load_mode

        (``str``)

        Defines how the *packed Python resources data* (see
        :ref:`python_packed_resources`) is written and loaded at run-time by the
        embedded Python interpreter.

        The following values/patterns can be defined:

        ``none``
           No resources data will be serialized or loaded at run-time. (Use this
           if you are using Python's filesystem based module importer and don't
           want to use PyOxidizer's custom importer.)

        ``embedded:<filename>``
           The packed resources data will be embedded in the binary and loaded
           from a memory address at run-time.

           ``filename`` denotes the path of the on-disk file used at build time.
           This file is written to the *artifacts* directory that PyOxidizer
           writes required build files to.

        ``binary-relative-memory-mapped:<filename>``
           The packed resources data will be written to a file relative to the
           built binary and loaded from there at run-time using memory mapped I/O.

        The default is ``embedded:packed-resources``.

    .. py:attribute:: tcl_files_path

        (``Optional[str]``)

        Defines a directory relative to that of the built executable in which to
        install tcl/tk files.

        If set to a value, tcl/tk files present in the Python distribution being
        used will be installed next to the build executable and the embedded Python
        interpreter will automatically set the ``TCL_LIBRARY`` environment variable
        to load tcl files from this directory.

        If ``None`` (the default), no tcl/tk files will be installed.

    .. py:attribute:: windows_runtime_dlls_mode

        (``str``)

        Controls how Windows runtime DLLs should be managed when building the binary.

        Windows binaries often have a dependency on various runtime DLLs, such as
        ``vcruntime140.dll``. The built executable will need access to these DLLs
        or it won't work.

        This setting controls whether to install required Windows runtime DLLs
        next to the built binary at build time. For example, if you are producing
        a ``myapp.exe``, this setting can automatically install a ``vcruntime140.dll``
        next to that binary.

        The following values are recognized:

        ``never``
           Never install Windows runtime DLLs.

        ``when-present``
           Install Windows runtime DLLs when they can be located. Do nothing if
           they can't be found.

        ``always``
           Install Windows runtime DLLs and fail if they can't be located.

        This setting is ignored when the built binary does not have a dependency
        on Windows runtime DLLs.

        See :ref:`pyoxidizer_distributing_windows` for more on runtime DLL requirements.

    .. py:attribute:: windows_subsystem

        (``str``)

        Controls the value to use for the Rust ``#![windows_subsystem = "..."]``
        attribute added to the autogenerated Rust program to build the executable.

        This attribute only has meaning on Windows. It effectively controls the
        value passed to the linker's ``/SUBSYSTEM`` flag.

        Rust only supports certain values but PyOxidizer does not impose limitations
        on what values are used. Common values include:

        ``console``
           Win32 character-mode application. A console window will be opened when the
           application runs.

           This value is suitable for command-line executables.

        ``windows``
           Application does not require a console and may provide its own windows.

           This value is suitable for GUI applications that do not wish to launch
           a console window on start.

        Default is ``console``.

    .. py:method:: make_python_module_source(name: str, source: str, is_package: bool) -> PythonModuleSource

        This method creates a :py:class:`PythonModuleSource` instance
        suitable for use with the executable being built.

        Arguments are as follows:

        ``name``
           The name of the Python module. This is the fully qualified module
           name. e.g. ``foo`` or ``foo.bar``.
        ``source``
           Python source code comprising the module.
        ``is_package``
           Whether the Python module is also a package. (e.g. the equivalent of a
           ``__init__.py`` file or a module without a ``.`` in its name.

    .. py:method:: pip_download(args: list[str]) -> list[Any]

        This method runs ``pip download <args>`` with settings appropriate to target
        the executable being built.

        This always uses ``--only-binary=:all:``, forcing pip to only download wheel
        based packages.

        This method accepts the following arguments:

        ``args``
           (``list`` of ``str``) Command line arguments to pass to ``pip download``.
           Arguments will be added after default arguments added internally.

        Returns a ``list`` of objects representing Python resources collected
        from wheels obtained via ``pip download``.

    .. py:method:: pip_install(args: list[str], extra_envs: Optional[dict[str, str]]) -> list[Any]

        This method runs ``pip install <args>`` with settings appropriate to target
        the executable being built.

        ``args``
           List of strings defining raw process arguments to pass to ``pip install``.

        ``extra_envs``
           Optional dict of string key-value pairs constituting extra environment
           variables to set in the invoked ``pip`` process.

        Returns a ``list`` of objects representing Python resources installed as
        part of the operation. The types of these objects can be
        :py:class:`PythonModuleSource`, :py:class:`PythonPackageResource`, etc.

        The returned resources are typically added to a
        :py:class:`starlark_tugger.FileManifest` or
        ``PythonExecutable`` to make them available to a packaged
        application.

    .. py:method:: read_package_root(path: str, packages: list[str]) -> list[Any]

        This method discovers resources from a directory on the filesystem.

        The specified directory will be scanned for resource files. However,
        only specific named *packages* will be found. e.g. if the directory
        contains sub-directories ``foo/`` and ``bar``, you must explicitly
        state that you want the ``foo`` and/or ``bar`` package to be included
        so files from these directories will be read.

        This rule is frequently used to pull in packages from local source
        directories (e.g. directories containing a ``setup.py`` file). This
        rule doesn't involve any packaging tools and is a purely driven by
        filesystem walking. It is primitive, yet effective.

        This rule has the following arguments:

        ``path``
           The filesystem path to the directory to scan.

        ``packages``
           List of package names to include.

           Filesystem walking will find files in a directory ``<path>/<value>/`` or in
           a file ``<path>/<value>.py``.

        Returns a ``list`` of objects representing Python resources found in the
        virtualenv. The types of these objects can be ``PythonModuleSource``,
        ``PythonPackageResource``, etc.

        The returned resources are typically added to a
        :py:class:`starlark_tugger.FileManifest` or
        ``PythonExecutable`` to make them available to a packaged application.

    .. py:method:: read_virtualenv(path: str) -> list[Any]

        This method attempts to read Python resources from an already built
        virtualenv.

        .. important::

           PyOxidizer only supports finding modules and resources
           populated via *traditional* means (e.g. ``pip install`` or ``python setup.py
           install``). If ``.pth`` or similar mechanisms are used for installing modules,
           files may not be discovered properly.

        It accepts the following arguments:

        ``path``
           The filesystem path to the root of the virtualenv.

           Python modules are typically in a ``lib/pythonX.Y/site-packages`` directory
           (on UNIX) or ``Lib/site-packages`` directory (on Windows) under this path.

        Returns a ``list`` of objects representing Python resources found in the virtualenv.
        The types of these objects can be ``PythonModuleSource``,
        ``PythonPackageResource``, etc.

        The returned resources are typically added to a
        :py:class:`starlark_tugger.FileManifest` or
        ``PythonExecutable`` to make them available to a packaged application.

    .. py:method:: setup_py_install(package_path: str, extra_envs: dict[str, str] = {}, extra_global_arguments: dict[str, str] = {}) -> list[Any]

        This method runs ``python setup.py install`` against a package at the
        specified path.

        It accepts the following arguments:

        ``package_path``
           String filesystem path to directory containing a ``setup.py`` to invoke.

        ``extra_envs={}``
           Optional dict of string key-value pairs constituting extra environment
           variables to set in the invoked ``python`` process.

        ``extra_global_arguments=[]``
           Optional list of strings of extra command line arguments to pass to
           ``python setup.py``. These will be added before the ``install``
           argument.

        Returns a ``list`` of objects representing Python resources installed
        as part of the operation. The types of these objects can be
        ``PythonModuleSource``, ``PythonPackageResource``, etc.

        The returned resources are typically added to a
        :py:class:`starlark_tugger.FileManifest` or
        ``PythonExecutable`` to make them available to a packaged application.

    .. py:method:: add_python_resource(resource: Union[PythonModuleSource, PythonPackageResource, PythonExtensionModule])

        This method registers a Python resource of various types with the instance.

        It accepts a ``resource`` argument which can be a ``PythonModuleSource``,
        ``PythonPackageResource``, or ``PythonExtensionModule`` and registers that
        resource with this instance.

        The following arguments are accepted:

        ``resource``
           The resource to add to the embedded Python environment.

        This method is a glorified proxy to the various ``add_python_*`` methods.
        Unlike those methods, this one accepts all types that are known Python
        resources.

    .. py:method:: add_python_resources(resources: list[Union[PythonModuleSource, PythonPackageResource, PythonExtensionModule])

        This method registers an iterable of Python resources of various types.
        This method is identical to :py:meth:`add_python_resource` except the argument is
        an iterable of resources. All other arguments are identical.

    .. py:method:: add_cargo_manifest_licensing(manifest_path: str, all_features: bool = False, features = None)

       Register software component licensing for a package defined in a ``Cargo.toml``
       manifest.

       This method accepts the following arguments:

       ``manifest_path``
         Filesystem path of ``Cargo.toml`` to process.

       ``all_features``
         Whether to activate all crate features when determining licensing info.

       ``features``
         List of strings denoting explicit features to enable.

         Ignored if ``all_features`` is enabled.

    .. py:method:: filter_resources_from_files(files: list[str], glob_files: list[str])

        This method filters all embedded resources (source modules, bytecode modules,
        and resource names) currently present on the instance through a set of
        resource names resolved from files.

        This method accepts the following arguments:

        ``files``
           List of filesystem paths to files containing resource names. The file
           must be valid UTF-8 and consist of a ``\n`` delimited list of resource
           names. Empty lines and lines beginning with ``#`` are ignored.

        ``glob_files``
           List of glob matching patterns of filter files to read. ``*`` denotes
           all files in a directory. ``**`` denotes recursive directories. This
           uses the Rust ``glob`` crate under the hood and the documentation for that
           crate contains more pattern matching info.

           The files read by this argument must be the same format as documented
           by the ``files`` argument.

        All defined files are first read and the resource names encountered are
        unioned into a set. This set is then used to filter entities currently
        registered with the instance.

    .. py:method:: to_embedded_resources()

        Obtains a :py:class:`PythonEmbeddedResources` instance representing
        resources to be made available to the Python interpreter.

        See the :py:class:`PythonEmbeddedResources` type documentation for more.

    .. py:method:: to_file_manifest(prefix: str) -> starlark_tugger.FileManifest

        This method transforms the ``PythonExecutable`` instance to a
        :py:class:`starlark_tugger.FileManifest`.
        The :py:class:`starlark_tugger.FileManifest` is populated with the build
        executable and any file-based resources that are registered with the
        resource collector. A ``libpython`` shared library will also be present
        depending on build settings.

        This method accepts the following arguments:

        ``prefix``
           The directory prefix of files in the
           :py:class:`starlark_tugger.FileManifest`. Use ``.`` to denote no prefix.

    .. py:method:: to_wix_bundle_builder(id_prefix: str, product_name: str, product_version: str, product_manufacturer: str, msi_builder_callback: Callable) -> starlark_tugger.WiXBundleBuilder

        This method transforms the ``PythonExecutable`` instance into a
        :py:class:`starlark_tugger.WiXBundleBuilder` instance. The returned value can
        be used to generate a Windows ``.exe`` installer. This installer will install
        the Visual C++ Redistributable as well as an MSI for the build application.

        This method accepts the following arguments:

        ``id_prefix``
           See :py:meth:`starlark_tugger.WiXMSIBuilder.__init__` for usage.
        ``product_name``
           See :py:meth:`starlark_tugger.WiXMSIBuilder.__init__` for usage.
        ``product_version``
           See :py:meth:`starlark_tugger.WiXMSIBuilder.__init__` for usage.
        ``product_manufacturer``
           See :py:meth:`starlark_tugger.WiXMSIBuilder.__init__` for usage.
        ``msi_builder_callback``
           (``function``) A callable function that can be used to modify the
           :py:class:`starlark_tugger.WiXMSIBuilder` constructed for the application.

           The function will receive the :py:class:`starlark_tugger.WiXMSIBuilder` as
           its single argument. The return value is ignored.

        The returned value can be further customized before it is built. See
        :py:class:`starlark_tugger.WiXBundleBuilder` type documentation for more.

        .. important::

           :py:attr:`PythonExecutable.windows_runtime_dlls_mode` can result
           in DLLs being installed next to the binary in addition to being installed
           as part of the installer. When using this method, you probably want to set
           ``.windows_runtime_dlls_mode = "never"`` to prevent the redundant
           installation.

    .. py:method:: to_wix_msi_builder(id_prefix: str, product_name: str, product_version: str, product_manufacturer: str) -> starlark_tugger.WiXMSIBuilder

        This method transforms the ``PythonExecutable`` instance into a
        :py:class:`starlark_tugger.WiXMSIBuilder` instance. The returned value can
        be used to generate a Windows MSI installer.

        This method accepts the following arguments:

        ``id_prefix``
           See :py:meth:`starlark_tugger.WiXMSIBuilder.__init__` for usage.
        ``product_name``
           See :py:meth:`starlark_tugger.WiXMSIBuilder.__init__` for usage.
        ``product_version``
           See :py:meth:`starlark_tugger.WiXMSIBuilder.__init__` for usage.
        ``product_manufacturer``
           See :py:meth:`starlark_tugger.WiXMSIBuilder.__init__` for usage.

        The MSI installer configuration can be customized. See the
        :py:class:`starlark_tugger.WiXMSIBuilder` type documentation for more.

        The MSI installer will **not** materialize the Visual C++ Runtime DLL(s).

    .. py:method:: build(target: str) -> starlark_tugger.ResolvedTarget

       Produces a binary executable embedding Python using the settings configured
       on this instance.

        ``target``
           The name of the target being built.

       Under the covers, this will generate a temporary Rust project and invoke
       ``cargo``, Rust's build tool, for generating an executable. The end result
       of this process is a single executable embedding a Python interpreter.

       Upon successful generation of a binary, the produced binary will be assessed
       for code signing with the ``python-executable-creation`` *action*.

    .. py:method:: write_licenses(path)

        Writes software component licensing info to the file specified via ``path``.

        The file will contain a bill of materials of all software components included
        in the resulting binary and licensing information related to them. This includes
        license texts, when available.

        Licensing info is best effort.
