.. py:currentmodule:: starlark_pyoxidizer

.. _config_files:

===================
Configuration Files
===================

PyOxidizer uses `Starlark <https://github.com/bazelbuild/starlark>`_
files to configure run-time behavior.

Starlark is a dialect of Python intended to be used as a configuration
language and the syntax should be familiar to any Python programmer.

This documentation section contains both a high-level overview of
the configuration files and their semantics as well as low-level
documentation for every type and function in the Starlark dialect.

.. toctree::
   :maxdepth: 3

   pyoxidizer_config_locating
   pyoxidizer_config_concepts
   pyoxidizer_config_resource_add_attributes
   pyoxidizer_config_globals
   pyoxidizer_config_global_state
   pyoxidizer_config_target_management
   pyoxidizer_config_tugger_extensions
   pyoxidizer_config_type_file
   pyoxidizer_config_type_python_distribution
   pyoxidizer_config_type_python_embedded_resources
   pyoxidizer_config_type_python_executable
   pyoxidizer_config_type_python_extension_module
   pyoxidizer_config_type_python_interpreter_config
   pyoxidizer_config_type_python_module_source
   pyoxidizer_config_type_python_package_resource
   pyoxidizer_config_type_python_package_distribution_resource
   pyoxidizer_config_type_python_packaging_policy
