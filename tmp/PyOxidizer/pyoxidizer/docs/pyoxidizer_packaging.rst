.. py:currentmodule:: starlark_pyoxidizer

.. _packaging:

====================
Packaging User Guide
====================

So you want to package a Python application using ``PyOxidizer``? You've come
to the right place to learn how! Read on for all the details on how to
*oxidize* your Python application!

First, you'll need to install ``PyOxidizer``. See :ref:`installing` for
instructions.

.. toctree::
   :maxdepth: 2

   pyoxidizer_packaging_creating_projects
   pyoxidizer_packaging_config_file
   pyoxidizer_packaging_python_distributions
   pyoxidizer_packaging_resources
   pyoxidizer_packaging_python_files
   pyoxidizer_packaging_additional_files
   pyoxidizer_packaging_extension_modules
   pyoxidizer_packaging_resources_data
   pyoxidizer_packaging_trimming_resources
   pyoxidizer_packaging_performance
   pyoxidizer_packaging_pitfalls
   pyoxidizer_packaging_masquerading
   pyoxidizer_packaging_static_linking
   pyoxidizer_packaging_licensing
   pyoxidizer_packaging_terminfo
   pyoxidizer_packaging_multiprocessing
   pyoxidizer_packaging_ssl_certificates
   pyoxidizer_packaging_tkinter
   pyoxidizer_packaging_python_executable
