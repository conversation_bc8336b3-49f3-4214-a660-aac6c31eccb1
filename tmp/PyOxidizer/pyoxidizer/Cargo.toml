[package]
name = "pyoxidizer"
version = "0.24.0"
authors = ["<PERSON> <<EMAIL>>"]
license = "MPL-2.0"
description = "Package self-contained Python applications"
readme = "../README.md"
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
keywords = ["python"]
edition = "2021"
build = "build.rs"

[[package.metadata.release.pre-release-replacements]]
file = "../pyoxidizer.bzl"
search = "PYOXIDIZER_VERSION = .*"
replace = "PYOXIDIZER_VERSION = \"{{version}}\""
exactly = 1

[[package.metadata.release.pre-release-replacmenets]]
file = "docs/pyoxidizer_history.rst"
search = "Next\n----"
replace = "{{version}}\n------"
exactly = 1

[[package.metadata.release.pre-release-replacements]]
file = "docs/pyoxidizer_history.rst"
search = "\\(Not yet released\\)"
replace = "Released {{date}}"
exactly = 1

[[package.metadata.release.pre-release-replacements]]
file = "docs/pyoxidizer_history.rst"
search = "\\.\\. next-release"
replace = ".. next-release\n\nNext\n----\n\n(Not yet released)"
exactly = 1

# We rename the library so we can have crate documentation for both the
# library and the binary.
[lib]
name = "pyoxidizerlib"
path = "src/lib.rs"
# The library offers no functionality that the binary doesn't and testing
# both is redundant and adds overhead. So we disable tests for the library.
test = false

[build-dependencies]
# We don't need network functionality. So disable default features which
# pull it in.
git2 = { version = "0.19.0", default-features = false }

[dependencies]
anyhow = "1.0.92"
apple-sdk = "0.6.0"
ar = "0.9.0"
cargo-lock = "10.0.1"
cargo_toml = "0.20.5"
cc = "1.1.34"
clap = { version = "4.5.20", features = ["string"] }
codemap = "0.1.3"
codemap-diagnostic = "0.1.2"
dirs = "5.0.1"
duct = "0.13.7"
env_logger = "0.11.5"
fs2 = "0.4.3"
glob = "0.3.1"
guppy = "0.17.8"
handlebars = "6.2.0"
hex = "0.4.3"
itertools = "0.13.0"
linked-hash-map = "0.5.6"
log = "0.4.22"
once_cell = "1.20.2"
path-dedot = "3.1.1"
pyo3-build-config = "0.18.3"
remove_dir_all = "0.8.4"
rustc_version = "0.4.1"
semver = "1.0.23"
serde = { version = "1.0.214", features = ["derive"] }
serde_json = "1.0.132"
sha2 = "0.10.8"
shlex = "1.3.0"
simple-file-manifest = "0.11.0"
starlark = "0.3.2"
tar = "0.4.43"
tempfile = "3.13.0"
url = "2.5.2"
uuid = { version = "1.11.0", features = ["v4", "v5"] }
version-compare = "0.2.0"
walkdir = "2.5.0"
which = "7.0.0"
zstd = "0.13.2"

[dependencies.python-packaging]
version = "0.16.0-pre"
path = "../python-packaging"
features = ["spdx-text"]

[dependencies.python-packed-resources]
version = "0.12.0-pre"
path = "../python-packed-resources"

[dependencies.starlark-dialect-build-targets]
version = "0.8.0-pre"
path = "../starlark-dialect-build-targets"

[dependencies.tugger-binary-analysis]
version = "0.7.0-pre"
path = "../tugger-binary-analysis"

[dependencies.tugger-code-signing]
version = "0.10.0-pre"
path = "../tugger-code-signing"

[dependencies.tugger-common]
version = "0.10.0-pre"
path = "../tugger-common"

[dependencies.tugger-rust-toolchain]
version = "0.13.0-pre"
path = "../tugger-rust-toolchain"

[dependencies.tugger-windows]
version = "0.10.0-pre"
path = "../tugger-windows"

[dependencies.tugger-wix]
version = "0.16.0-pre"
path = "../tugger-wix"

[dependencies.tugger]
version = "0.18.0-pre"
path = "../tugger"

[dev-dependencies]
assert_cmd = "2.0.16"
assert_fs = "1.1.2"
indoc = "2.0.5"
libtest-mimic = "0.8.1"
predicates = "3.1.2"
trycmd = "0.15.8"
xml-rs = "0.8.22"

[[test]]
name = "cli_generate_python_embedding_artifacts"
harness = false
