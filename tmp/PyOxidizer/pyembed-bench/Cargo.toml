[package]
name = "pyembed-bench"
version = "0.1.0-pre"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
license = "Python-2.0 OR MPL-2.0"
description = "Benchmarks for the pyembed crate"
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"

[package.metadata.release]
# Internal package is never released.
release = false

[dependencies]
anyhow = "1.0.92"
once_cell = "1.20.2"
pyo3 = "0.18.3"
tempfile = "3.13.0"
zip = { version = "2.2.0", default-features = false, features = ["deflate"] }

[dependencies.pyembed]
version = "0.25.0-pre"
path = "../pyembed"
default-features = false
features = ["zipimport"]

[dependencies.pyoxidizer]
version = "0.24.0-pre"
path = "../pyoxidizer"

[dependencies.python-oxidized-importer]
version = "0.9.0-pre"
path = "../python-oxidized-importer"
default-features = false
features = ["zipimport"]

[dependencies.python-packaging]
version = "0.16.0-pre"
path = "../python-packaging"

[dependencies.python-packed-resources]
version = "0.12.0-pre"
path = "../python-packed-resources"

[dev-dependencies]
criterion = "0.5.1"

[[bench]]
name = "zip"
harness = false

[[bench]]
name = "oxidized_finder"
harness = false

[[bench]]
name = "embedded_interpreter"
harness = false
