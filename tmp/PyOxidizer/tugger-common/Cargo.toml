[package]
name = "tugger-common"
version = "0.10.0"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2021"
license = "MPL-2.0"
description = "Common functionality used by various tugger crates"
keywords = ["tugger"]
homepage = "https://github.com/indygreg/PyOxidizer"
repository = "https://github.com/indygreg/PyOxidizer.git"
readme = "README.md"

[dependencies]
anyhow = "1.0.92"
fs2 = "0.4.3"
glob = "0.3.1"
hex = "0.4.3"
log = "0.4.22"
once_cell = "1.20.2"
reqwest = { version = "0.12.9", default-features= false, features = ["blocking", "rustls-tls"] }
sha2 = "0.10.8"
tempfile = "3.13.0"
url = "2.5.2"
zip = { version = "2.2.0", default-features = false, features = ["deflate"] }
