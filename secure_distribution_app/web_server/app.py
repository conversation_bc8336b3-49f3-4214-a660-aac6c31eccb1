"""
Flask Web Server for Custom Template Authentication

Serves the existing HTML templates and handles authentication through custom forms.
"""

import os
import sys
import logging
import subprocess
import shutil
from pathlib import Path
from flask import Flask, render_template, render_template_string, request, jsonify, session, redirect, url_for, flash
import secrets
from datetime import datetime, timezone, timedelta
from textwrap import dedent

# Add parent directory to path
# Ensure project root and secure app directories are importable
project_root = Path(__file__).resolve().parents[2]
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import Supabase and authentication modules
try:
    from supabase import create_client, Client
    from auth.license_manager import LicenseManager
except ImportError:
    logging.getLogger().error("Required dependency missing: supabase. Please install it with 'pip install supabase'.")
    sys.exit(1)

# Configure logging
WEB_SILENT = os.getenv('WEB_SILENT', 'false').lower() == 'true' or os.getenv('SECURE_BUILD', 'false').lower() == 'true'
root_logger = logging.getLogger()
root_logger.handlers = []
root_logger.setLevel(logging.WARNING if WEB_SILENT else logging.INFO)
logs_dir = Path(__file__).parent.parent / 'logs'
try:
    logs_dir.mkdir(parents=True, exist_ok=True)
    file_handler = logging.FileHandler(str(logs_dir / 'web_server.log'))
    file_handler.setFormatter(logging.Formatter('%(asctime)s %(levelname)s [%(name)s] %(message)s'))
    root_logger.addHandler(file_handler)
except Exception:
    pass
if not WEB_SILENT:
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))
    root_logger.addHandler(console_handler)
logging.getLogger('werkzeug').setLevel(logging.ERROR if WEB_SILENT else logging.WARNING)
logger = logging.getLogger(__name__)

# Load environment variables
def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            logger.info("Environment variables loaded")
            return True
    except Exception as e:
        logger.error(f"Failed to load environment: {e}")
    return False

# Load environment
load_environment()

# Resolve template/static paths for both source and PyInstaller bundle
try:
    if getattr(sys, 'frozen', False):
        base_root = Path(sys._MEIPASS)  # PyInstaller temporary extraction dir
    else:
        base_root = Path(__file__).resolve().parent.parent.parent
    templates_dir = base_root / "templates"
    static_dir = templates_dir
except Exception:
    # Fallback to relative paths
    base_root = Path(__file__).resolve().parent.parent.parent
    templates_dir = base_root / "templates"
    static_dir = templates_dir

# Initialize Flask app
app = Flask(
    __name__,
    template_folder=str(templates_dir),
    static_folder=str(static_dir)
)

# Configure secret key for sessions
app.secret_key = secrets.token_hex(32)
# Use a distinct session cookie name to avoid conflicts with platform apps
app.config.update(
    SESSION_COOKIE_NAME='web_session',
    SESSION_COOKIE_SAMESITE='Lax',
    SESSION_COOKIE_SECURE=False
)

# Initialize Supabase client
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY')

if not SUPABASE_URL or not SUPABASE_ANON_KEY:
    logger.error("Missing Supabase credentials in environment")
    sys.exit(1)

try:
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
    logger.info("Supabase client initialized successfully")
except Exception:
    logger.error("Failed to initialize Supabase client")
    sys.exit(1)

# Initialize License Manager for device fingerprinting
# Optional Grid Manager import for Grid control/status
try:
    from grid_manager import get_grid_manager, NodeStatus, DevicePlatform
    GRID_AVAILABLE = True
except Exception as e:
    GRID_AVAILABLE = False
    logger.warning(f"Grid manager not available: {e}")

# ---- Port utilities (cleanup and discovery) ---------------------------------
import socket
import platform as _plat

def _is_port_open(port: int, host: str = '127.0.0.1') -> bool:
    try:
        with socket.create_connection((host, port), timeout=0.5):
            return True
    except Exception:
        return False

def _find_free_port(start: int, end: int) -> int:
    for p in range(start, end + 1):
        if not _is_port_open(p):
            return p
    return 0

def _kill_process_on_port(port: int) -> list:
    """Best-effort kill of any process listening on the port. Returns list of PIDs killed."""
    killed = []
    try:
        if _plat.system().lower() == 'windows':
            # netstat /ano | findstr :PORT
            import subprocess as _sp
            out = _sp.check_output(['powershell','-NoProfile','-Command', f"(Get-NetTCPConnection -LocalPort {port} -ErrorAction SilentlyContinue).OwningProcess"], text=True)
            for line in out.strip().splitlines():
                pid = line.strip()
                if pid.isdigit():
                    try:
                        _sp.run(['taskkill','/PID', pid, '/F'], check=False, stdout=_sp.DEVNULL, stderr=_sp.DEVNULL)
                        killed.append(int(pid))
                    except Exception:
                        pass
        else:
            import subprocess as _sp
            # macOS/Linux: lsof -tiTCP:port -sTCP:LISTEN | xargs kill -9
            p = _sp.Popen(f"lsof -tiTCP:{port} -sTCP:LISTEN", shell=True, stdout=_sp.PIPE, stderr=_sp.PIPE, text=True)
            stdout, _ = p.communicate(timeout=2)
            for line in stdout.strip().split():
                if line.isdigit():
                    try:
                        _sp.run(['kill','-9', line], check=False, stdout=_sp.DEVNULL, stderr=_sp.DEVNULL)
                        killed.append(int(line))
                    except Exception:
                        pass
    except Exception:
        pass
    return killed

try:
    license_manager = LicenseManager(supabase)
    logger.info("License manager initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize license manager: {e}")
    license_manager = None

# Supabase authentication functions
def create_user_profile(user_id: str, email: str, device_fingerprint: str = None) -> bool:
    """Create user profile in Supabase"""
    try:
        # Generate device fingerprint if not provided
        if not device_fingerprint and license_manager:
            device_fingerprint = license_manager.generate_hardware_fingerprint()

        profile_data = {
            'user_id': user_id,
            'device_fingerprint': device_fingerprint,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat(),
            'metadata': {
                'email': email,
                'login_count': 1
            }
        }

        result = supabase.table('user_profiles').insert(profile_data).execute()
        logger.info(f"User profile created for {email}")
        return True

    except Exception as e:
        logger.error(f"Failed to create user profile: {e}")
        return False

def get_user_profile(user_id: str) -> dict:
    """Get user profile from Supabase"""
    try:
        result = supabase.table('user_profiles').select('*').eq('user_id', user_id).execute()
        if result.data:
            return result.data[0]
        return None
    except Exception as e:
        logger.error(f"Failed to get user profile: {e}")
        return None

def update_user_profile(user_id: str, updates: dict) -> bool:
    """Update user profile in Supabase"""
    try:
        updates['updated_at'] = datetime.now(timezone.utc).isoformat()
        supabase.table('user_profiles').update(updates).eq('user_id', user_id).execute()
        return True
    except Exception as e:
        logger.error(f"Failed to update user profile: {e}")
        return False


def _parse_iso_date(value: str) -> str:
    """Convert ISO timestamp to a friendly display string."""
    if not value:
        return None
    try:
        dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
        return dt.astimezone(timezone.utc).strftime('%b %d, %Y at %I:%M %p UTC')
    except Exception:
        return value


def _build_premium_profile(user_obj, profile: dict) -> dict:
    """Construct premium profile details with safe fallbacks."""
    metadata = (profile or {}).get('metadata') or {}

    subscription_seed = (getattr(user_obj, 'id', '') or 'PREMIUM')[:8].upper()
    subscription_id = metadata.get('subscription_id') or profile.get('license_number') if profile else None
    if not subscription_id:
        subscription_id = f"PREM-{subscription_seed}" if subscription_seed else 'PREM-GUEST'

    expiry_raw = metadata.get('subscription_expires_on') if isinstance(metadata, dict) else None
    expiry_display = _parse_iso_date(expiry_raw)
    if not expiry_display:
        expiry_display = (datetime.now(timezone.utc) + timedelta(days=30)).strftime('%b %d, %Y')

    last_login_raw = metadata.get('last_login') if isinstance(metadata, dict) else None
    if not last_login_raw:
        last_login_raw = getattr(user_obj, 'last_sign_in_at', None)
    last_login_display = _parse_iso_date(last_login_raw) or 'Awaiting activity'

    duration_raw = metadata.get('last_session_duration') if isinstance(metadata, dict) else None
    if duration_raw is None:
        duration_display = 'Tracking in progress'
    else:
        try:
            duration_display = _fmt_duration(int(duration_raw))
        except Exception:
            duration_display = str(duration_raw)

    return {
        'subscription_id': subscription_id,
        'subscription_expires_on': expiry_display,
        'last_login_at': last_login_display,
        'last_session_duration': duration_display,
    }


def _build_dashboard_stats(profile: dict) -> dict:
    """Return dashboard metrics with sensible defaults."""
    defaults = {
        'total_test_suites': 0,
        'android_suites': 0,
        'ios_suites': 0,
        'success_rate': 0,
        'android_devices': 0,
        'ios_devices': 0,
        'android_last_run': 'Never',
        'ios_last_run': 'Never',
        'android_status': 'Ready',
        'ios_status': 'Ready',
    }

    metadata = (profile or {}).get('metadata') or {}
    if isinstance(metadata, dict):
        stats_source = metadata.get('dashboard_stats') if isinstance(metadata.get('dashboard_stats'), dict) else metadata
        for key in defaults:
            value = stats_source.get(key) if isinstance(stats_source, dict) else None
            if value is not None:
                defaults[key] = value

    # Ensure numeric fields are cast appropriately when possible
    for numeric_key in ('total_test_suites', 'android_suites', 'ios_suites', 'success_rate', 'android_devices', 'ios_devices'):
        try:
            defaults[numeric_key] = int(defaults[numeric_key])
        except Exception:
            pass

    return defaults

# ---- Session Analytics (Supabase) --------------------------------------------

def _client_ip() -> str:
    try:
        # Respect proxy headers if present (local only)
        fwd = request.headers.get('X-Forwarded-For')
        if fwd:
            return fwd.split(',')[0].strip()
        return request.remote_addr or '127.0.0.1'
    except Exception:
        return '127.0.0.1'

def record_session_start(user_id: str) -> None:
    """Persist session start information without assuming optional columns exist."""
    payload = {
        'user_id': user_id,
        'login_timestamp': datetime.now(timezone.utc).isoformat(),
        'platform_launched': None
    }

    include_ip = os.getenv('SUPABASE_INCLUDE_SESSION_IP', 'false').lower() == 'true'
    if include_ip:
        payload['client_ip_address'] = _client_ip()

    try:
        supabase.table('user_sessions').insert(payload).execute()
    except Exception as e:
        # Some Supabase environments may not include optional columns like client_ip_address.
        if include_ip and ('client_ip_address' in str(e)):
            payload.pop('client_ip_address', None)
            try:
                supabase.table('user_sessions').insert(payload).execute()
                logger.info('user_sessions insert retried without client_ip_address column')
                return
            except Exception as inner:
                logger.warning(f"user_sessions insert retry failed: {inner}")
        logger.warning(f"user_sessions insert failed (start): {e}")

def _get_latest_open_session(user_id: str):
    try:
        # Prefer open session (no logout) else latest any
        res = supabase.table('user_sessions').select('*').eq('user_id', user_id).is_('logout_timestamp', 'null').order('login_timestamp', desc=True).limit(1).execute()
        if res.data:
            return res.data[0]
        res2 = supabase.table('user_sessions').select('*').eq('user_id', user_id).order('login_timestamp', desc=True).limit(1).execute()
        if res2.data:
            return res2.data[0]
    except Exception as e:
        logger.warning(f"fetch latest session failed: {e}")
    return None

def record_session_end(user_id: str) -> None:
    try:
        row = _get_latest_open_session(user_id)
        if not row:
            return
        login_ts = row.get('login_timestamp')
        session_id = row.get('id')
        if not session_id:
            return
        try:
            start_dt = datetime.fromisoformat(login_ts.replace('Z','+00:00')) if isinstance(login_ts, str) else datetime.now(timezone.utc)
        except Exception:
            start_dt = datetime.now(timezone.utc)
        end_dt = datetime.now(timezone.utc)
        dur = int((end_dt - start_dt).total_seconds())
        supabase.table('user_sessions').update({
            'logout_timestamp': end_dt.isoformat(),
            'session_duration_seconds': dur
        }).eq('id', session_id).execute()
    except Exception as e:
        logger.warning(f"user_sessions update failed (end): {e}")

def record_platform_launched(user_id: str, platform: str) -> None:
    try:
        row = _get_latest_open_session(user_id)
        if not row:
            return
        current = (row.get('platform_launched') or '').lower()
        new_val = platform
        if current and current != platform:
            new_val = 'both'
        supabase.table('user_sessions').update({'platform_launched': new_val}).eq('id', row.get('id')).execute()
    except Exception as e:
        logger.warning(f"user_sessions update failed (platform): {e}")


_SHARED_DB_FALLBACK_SOURCE = dedent(
    """
    import logging

    logger = logging.getLogger(__name__)


    class SharedDirectoryPathsDB:
        '''Fallback stub when the shared_directory_paths_db module is unavailable.'''

        def __init__(self, platform='ios'):
            self.platform = platform
            self._paths = {}
            logger.warning(
                "Using in-memory SharedDirectoryPathsDB fallback. Distribution build is missing shared_directory_paths_db.py."
            )

        def get_path(self, name, default=None):
            return self._paths.get(name, default)

        def save_all_paths(self, paths_dict):
            if isinstance(paths_dict, dict):
                self._paths.update(paths_dict)
            return True

        def get_all_paths(self):
            return dict(self._paths)

        def save_path(self, name, path):
            self._paths[name] = path
            return True

        def get_all_environments(self):
            return []
    """
)


def _resolve_launch_script(entry_name: str, override_path: str | None = None) -> Path | None:
    """Determine the launcher script, allowing secure fallbacks when PyArmor assets are absent."""
    secure_root = Path(__file__).resolve().parents[1]
    project_root = Path(__file__).resolve().parents[2]
    build_root = secure_root / 'temp_build_final'
    obf_dir = build_root / 'obfuscated'
    strategy = (os.getenv('SECURE_BUILD_OBFUSCATION') or '').lower()

    def _is_allowed(path: Path) -> bool:
        try:
            resolved = path.resolve()
        except Exception:
            return False
        allowed_roots = [build_root.resolve(), secure_root.resolve(), project_root.resolve()]
        for root in allowed_roots:
            try:
                if resolved == root or resolved.is_relative_to(root):
                    return True
            except AttributeError:
                if str(resolved).startswith(str(root)):
                    return True
        return False

    candidates: list[Path] = []
    if override_path:
        override = Path(override_path)
        if override.exists() and _is_allowed(override):
            candidates.append(override)
        else:
            logger.warning(f"Override launch script rejected (unsafe or missing): {override_path}")

    preferred = [
        obf_dir / entry_name,
        build_root / entry_name,
        secure_root / entry_name,
        project_root / entry_name,
    ]

    for candidate in preferred:
        if candidate.exists() and _is_allowed(candidate):
            candidates.append(candidate)

    if not candidates:
        return None

    def _priority(path: Path) -> tuple[int, int]:
        resolved = path.resolve()
        obf_target = (obf_dir / entry_name).resolve()
        if strategy == 'pyarmor':
            is_obf = resolved == obf_target or obf_target in resolved.parents
            return (0 if is_obf else 1, len(str(resolved)))
        return (0 if resolved == obf_target else 1, len(str(resolved)))

    candidates.sort(key=_priority)
    selected = candidates[0]
    try:
        obf_target = (obf_dir / entry_name).resolve()
        selected_resolved = selected.resolve()
        if strategy == 'pyarmor' and selected_resolved != obf_target:
            logger.warning(
                "PyArmor strategy requested but using fallback launcher: %s",
                selected
            )
        elif strategy != 'pyarmor' and selected_resolved != obf_target and (obf_dir / entry_name).exists():
            logger.info("Using non-obfuscated launcher for %s", entry_name)
    except Exception:
        pass
    return selected


def _ensure_shared_directory_module(script_path: Path) -> Path:
    """Ensure shared_directory_paths_db module exists alongside the launcher."""
    target = script_path.parent / 'shared_directory_paths_db.py'
    if target.exists():
        return target

    secure_root = Path(__file__).resolve().parents[1]
    candidates = [
        secure_root / 'shared_directory_paths_db.py',
        secure_root.parent / 'shared_directory_paths_db.py',
    ]

    for candidate in candidates:
        if candidate.exists():
            try:
                shutil.copy2(candidate, target)
                logger.info(f"Copied shared_directory_paths_db.py to {target}")
                return target
            except Exception as copy_error:
                logger.warning(f"Failed to copy shared_directory_paths_db.py from {candidate}: {copy_error}")

    try:
        target.write_text(_SHARED_DB_FALLBACK_SOURCE, encoding='utf-8')
        logger.warning(f"Wrote fallback shared_directory_paths_db.py to {target}")
    except Exception as write_error:
        logger.error(f"Failed to create fallback shared_directory_paths_db.py: {write_error}")
    return target

def _fmt_human_dt(dt_iso: str) -> str:
    try:
        dt = datetime.fromisoformat(dt_iso.replace('Z','+00:00'))
        return dt.astimezone(timezone.utc).strftime('%B %-d, %Y at %-I:%M %p')
    except Exception:
        return dt_iso or '--'

def _fmt_duration(seconds: int) -> str:
    try:
        minutes, sec = divmod(int(seconds), 60)
        hours, minutes = divmod(minutes, 60)
        parts = []
        if hours:
            parts.append(f"{hours} hour{'s' if hours!=1 else ''}")
        if minutes:
            parts.append(f"{minutes} minute{'s' if minutes!=1 else ''}")
        if not parts:
            parts.append(f"{sec} seconds")
        return ' '.join(parts)
    except Exception:
        return '--'

@app.get('/api/analytics/session_summary')
def api_session_summary():
    try:
        uid = session.get('user_id')
        if not uid:
            return jsonify({}), 200
        # Last login and previous duration
        last_login = '--'; prev_dur = '--'
        res = supabase.table('user_sessions').select('*').eq('user_id', uid).order('login_timestamp', desc=True).limit(2).execute()
        if res.data:
            last_login = _fmt_human_dt(res.data[0].get('login_timestamp','--'))
            if len(res.data) > 1 and res.data[1].get('session_duration_seconds'):
                prev_dur = _fmt_duration(res.data[1]['session_duration_seconds'])
        # Counts week/month
        now = datetime.now(timezone.utc)
        start_week = now - timedelta(days=now.weekday())
        start_week = start_week.replace(hour=0, minute=0, second=0, microsecond=0)
        start_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        w = supabase.table('user_sessions').select('id', count='exact').eq('user_id', uid).gte('login_timestamp', start_week.isoformat()).execute()
        m = supabase.table('user_sessions').select('id', count='exact').eq('user_id', uid).gte('login_timestamp', start_month.isoformat()).execute()
        return jsonify({
            'last_login': last_login,
            'previous_session_duration': prev_dur,
            'sessions_this_week': (w.count if hasattr(w,'count') else (w.data and len(w.data))) or 0,
            'sessions_this_month': (m.count if hasattr(m,'count') else (m.data and len(m.data))) or 0,
        })
    except Exception as e:
        logger.warning(f"session_summary error: {e}")
        return jsonify({}), 200

@app.route('/')
def index():
    """Home page. If authenticated, send user to dashboard."""
    try:
        if session.get('authenticated') and session.get('access_token'):
            return redirect(url_for('dashboard'))
    except Exception:
        # If any issue with session, fall through to landing page
        pass
    return render_template('index.html')

@app.route('/login')
def login_page():
    """Login page. Redirect authenticated users to dashboard."""
    try:
        if session.get('authenticated') and session.get('access_token'):
            return redirect(url_for('dashboard'))
    except Exception:
        pass
    return render_template('login.html')

@app.route('/register')
def register_page():
    """Register page"""
    try:
        if session.get('authenticated') and session.get('access_token'):
            return redirect(url_for('dashboard'))
    except Exception:
        pass
    return render_template('register.html')

@app.route('/dashboard')
def dashboard():
    """Dashboard page - requires authentication"""
    if 'access_token' not in session:
        flash('Please log in to access the dashboard.', 'warning')
        return redirect(url_for('login_page'))

    # Validate Supabase session
    try:
        user = supabase.auth.get_user(session['access_token'])
        if not user:
            flash('Session expired. Please log in again.', 'warning')
            session.clear()
            return redirect(url_for('login_page'))

        # Enforce license compliance before dashboard access
        if license_manager:
            try:
                license_valid = license_manager.enforce_license_compliance(user.user.id)
                if not license_valid:
                    validation_result = license_manager.validate_license(user.user.id)
                    error_msg = validation_result.get('error', 'License validation failed')
                    flash(f'Access denied: {error_msg}', 'error')
                    session.clear()
                    return redirect(url_for('login_page'))

                # Update last validation timestamp
                license_manager.update_last_validation(user.user.id)

            except Exception as e:
                logger.error(f"License enforcement error in dashboard: {e}")
                flash('License validation error. Please contact support.', 'error')
                session.clear()
                return redirect(url_for('login_page'))

        # Get user profile and build dashboard context
        profile = get_user_profile(user.user.id)
        premium_profile = _build_premium_profile(user.user, profile)
        stats = _build_dashboard_stats(profile)

        # Persist premium profile highlights in session for reuse
        session['premium_profile'] = premium_profile

        return render_template(
            'dashboard.html',
            user=user.user,
            profile=profile,
            premium_profile=premium_profile,
            stats=stats
        )

    except Exception as e:
        logger.error(f"Dashboard access error: {e}")
        flash('Session validation failed. Please log in again.', 'error')
        session.clear()
        return redirect(url_for('login_page'))

@app.route('/api/auth/login', methods=['POST'])
def api_login():
    """Handle login API request with Supabase authentication"""
    try:
        data = request.get_json()
        email = data.get('email', '').lower().strip()
        password = data.get('password', '')

        logger.info(f"Supabase login attempt for email: {email}")

        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400

        # Authenticate with Supabase
        try:
            auth_response = supabase.auth.sign_in_with_password({
                "email": email,
                "password": password
            })

            if not auth_response.user:
                logger.warning(f"Supabase authentication failed for: {email}")
                return jsonify({'error': 'Invalid email or password'}), 401

            user = auth_response.user
            session_data = auth_response.session

            # Create or update user profile
            profile = get_user_profile(user.id)
            if not profile:
                # Create new profile
                device_fingerprint = license_manager.generate_hardware_fingerprint() if license_manager else None
                create_user_profile(user.id, email, device_fingerprint)
                profile = get_user_profile(user.id)
            else:
                # Update login count
                metadata = profile.get('metadata', {})
                metadata['login_count'] = metadata.get('login_count', 0) + 1
                metadata['last_login'] = datetime.now(timezone.utc).isoformat()
                update_user_profile(user.id, {'metadata': metadata})

            # Validate license before allowing login
            if license_manager:
                try:
                    license_valid = license_manager.enforce_license_compliance(user.id)
                    if not license_valid:
                        validation_result = license_manager.validate_license(user.id)
                        error_msg = validation_result.get('error', 'License validation failed')
                        logger.warning(f"License validation failed for user {email}: {error_msg}")
                        return jsonify({'error': f'Access denied: {error_msg}'}), 403

                    # Bind license to hardware if not already bound
                    if not profile.get('device_fingerprint'):
                        license_manager.bind_license_to_hardware(user.id)

                    # Update last validation timestamp
                    license_manager.update_last_validation(user.id)

                except Exception as e:
                    logger.error(f"License enforcement error during login: {e}")
                    return jsonify({'error': 'License validation error. Please contact support.'}), 500

            # Store session data
            session['access_token'] = session_data.access_token
            session['refresh_token'] = session_data.refresh_token
            session['user_id'] = user.id
            session['user_email'] = email
            session['authenticated'] = True
            # Record session start for analytics (best effort)
            try:
                record_session_start(user.id)
            except Exception as _:
                pass

            logger.info(f"Supabase login successful for user: {email}")

            return jsonify({
                'success': True,
                'message': 'Login successful',
                'access_token': session_data.access_token,
                'user': {
                    'id': user.id,
                    'email': email,
                    'created_at': user.created_at,
                    'profile': profile
                }
            })

        except Exception as auth_error:
            logger.error(f"Supabase authentication error: {auth_error}")
            return jsonify({'error': 'Invalid email or password'}), 401

    except Exception as e:
        logger.error(f"Login error: {e}")
        return jsonify({'error': 'An error occurred during login'}), 500

@app.route('/api/auth/establish_session', methods=['POST'])
def api_establish_session():
    """Establish a Flask session from a supplied Bearer access token.
    This allows native clients that already have a Supabase access token to create a server-side session
    without prompting for credentials again.
    """
    try:
        authz = request.headers.get('Authorization', '')
        if not authz.lower().startswith('bearer '):
            return jsonify({'error': 'Authorization header with Bearer token required'}), 401
        token = authz.split(' ', 1)[1].strip()
        # Validate token with Supabase
        try:
            user = supabase.auth.get_user(token)
            if not user:
                return jsonify({'error': 'Invalid token'}), 401
        except Exception as e:
            logger.error(f"Token validation failed: {e}")
            return jsonify({'error': 'Token validation failed'}), 401
        # Set session
        session['access_token'] = token
        session['user_email'] = getattr(user.user, 'email', None) if hasattr(user, 'user') else None
        session['authenticated'] = True
        logger.info(f"Established session from token for user: {session.get('user_email')}")
        return jsonify({'success': True, 'user_email': session.get('user_email')})
    except Exception as e:
        logger.error(f"Establish session error: {e}")
        return jsonify({'error': 'Failed to establish session'}), 500


# Helper to establish Flask session from Authorization: Bearer header when cookies are missing
# This makes native GUI and other clients more resilient to cookie loss and preserves SSO
# Returns (user, error_msg). On success, user is a Supabase user object and error_msg is None.
# On failure, user is None and error_msg contains a short message.
def _ensure_session_from_authorization():
    try:
        # If we already have a valid session, validate quickly and return
        if 'access_token' in session:
            try:
                _user = supabase.auth.get_user(session['access_token'])
                if _user:
                    return _user, None
            except Exception:
                # Fall through to re-validate via header
                pass
        authz = request.headers.get('Authorization', '')
        if not authz.lower().startswith('bearer '):
            return None, 'Missing bearer token'
        token = authz.split(' ', 1)[1].strip()
        try:
            user = supabase.auth.get_user(token)
            if not user:
                return None, 'Invalid token'
        except Exception as e:
            logger.error(f"Header token validation failed: {e}")
            return None, 'Token validation failed'
        # Populate server session to enable subsequent requests without header
        session['access_token'] = token
        session['user_email'] = getattr(user.user, 'email', None) if hasattr(user, 'user') else None
        session['authenticated'] = True
        logger.info(f"Session established from Authorization header for user: {session.get('user_email')}")
        return user, None
    except Exception as e:
        logger.error(f"_ensure_session_from_authorization error: {e}")
        return None, 'Internal error'


@app.route('/api/auth/register', methods=['POST'])
def api_register():
    """Handle registration API request with Supabase"""
    try:
        data = request.get_json()
        email = data.get('email', '').lower().strip()
        password = data.get('password', '')
        first_name = data.get('first_name', '').strip()
        last_name = data.get('last_name', '').strip()
        license_number = data.get('license_number', '').strip()

        logger.info(f"Supabase registration attempt for email: {email}")

        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400

        if len(password) < 6:
            return jsonify({'error': 'Password must be at least 6 characters long'}), 400

        # Register with Supabase
        try:
            auth_response = supabase.auth.sign_up({
                "email": email,
                "password": password,
                "options": {
                    "data": {
                        "first_name": first_name,
                        "last_name": last_name
                    }
                }
            })

            if not auth_response.user:
                return jsonify({'error': 'Registration failed'}), 400

            user = auth_response.user

            # Create user profile
            device_fingerprint = license_manager.generate_hardware_fingerprint() if license_manager else None
            profile_created = create_user_profile(user.id, email, device_fingerprint)

            if profile_created and license_number:
                # Update profile with license number
                update_user_profile(user.id, {'license_number': license_number})

            logger.info(f"Supabase registration successful for user: {email}")

            return jsonify({
                'success': True,
                'message': 'Registration successful. Please check your email for verification.',
                'user': {
                    'id': user.id,
                    'email': email,
                    'created_at': user.created_at
                }
            })

        except Exception as auth_error:
            logger.error(f"Supabase registration error: {auth_error}")
            error_msg = str(auth_error)
            if 'already registered' in error_msg.lower():
                return jsonify({'error': 'Email already registered'}), 409
            return jsonify({'error': 'Registration failed'}), 400

    except Exception as e:
        logger.error(f"Registration error: {e}")
        return jsonify({'error': 'An error occurred during registration'}), 500

@app.route('/api/auth/logout', methods=['POST'])
def api_logout():
    """Handle logout API request with Supabase"""
    try:
        # Sign out from Supabase if we have a session
        uid = session.get('user_id')
        if 'access_token' in session:
            try:
                supabase.auth.sign_out()
            except Exception as e:
                logger.warning(f"Supabase logout error: {e}")

        # Record session end
        try:
            if uid:
                record_session_end(uid)
        except Exception:
            pass

        session.clear()
        # Return JSON for API-oriented callers; base.html's logout() expects JSON
        return jsonify({'success': True, 'message': 'Logged out successfully'})
    except Exception as e:
        logger.error(f"Logout error: {e}")
        return jsonify({'error': 'An error occurred during logout'}), 500

@app.route('/api/session/create', methods=['POST'])
def api_create_session():
    """Create session for platform launch"""
    try:
        # Ensure we have a valid session; if not, try Authorization header
        user = None
        if 'access_token' not in session:
            user, err = _ensure_session_from_authorization()
            if not user:
                return jsonify({'error': err or 'Not authenticated'}), 401
        else:
            # Validate Supabase session
            try:
                user = supabase.auth.get_user(session['access_token'])
                if not user:
                    return jsonify({'error': 'Invalid session'}), 401
            except Exception:
                # Attempt recovery from Authorization header
                user, err = _ensure_session_from_authorization()
                if not user:
                    return jsonify({'error': 'Session validation failed'}), 401

        data = request.get_json()
        platform = data.get('platform')

        if platform not in ['android', 'ios']:
            return jsonify({'error': 'Invalid platform'}), 400

        # Generate session ID
        session_id = secrets.token_hex(16)

        logger.info(f"Created session {session_id} for platform {platform} (user: {user.user.email})")

        return jsonify({
            'success': True,
            'session_id': session_id,
            'platform': platform
        })

    except Exception as e:
        logger.error(f"Session creation error: {e}")
        return jsonify({'error': 'Failed to create session'}), 500

@app.route('/app_android')
@app.route('/app_android/<session_id>')
def app_android(session_id=None):
    """Launch Android automation app with dynamic port management"""
    try:
        user = None
        if 'access_token' not in session:
            # Attempt to establish session from Authorization header for native clients
            user, err = _ensure_session_from_authorization()
            if not user:
                flash('Please log in to access applications.', 'warning')
                return redirect(url_for('login_page'))
        else:
            # Validate Supabase session, fallback to Authorization header
            try:
                user = supabase.auth.get_user(session['access_token'])
                if not user:
                    flash('Session expired. Please log in again.', 'warning')
                    session.clear()
                    return redirect(url_for('login_page'))
            except Exception:
                user, err = _ensure_session_from_authorization()
                if not user:
                    flash('Session validation failed. Please log in again.', 'error')
                    session.clear()
                    return redirect(url_for('login_page'))

        logger.info(f"Launching Android app for user: {user.user.email}")

        # Resolve project root robustly (prefer directory that contains both run_android.py and app_android/)
        def _find_project_root():
            here = Path(__file__).resolve()
            for p in [here] + list(here.parents):
                if (p / 'run_android.py').exists() and (p / 'app_android').exists():
                    return p
            # Fallback to two levels up
            return Path(__file__).resolve().parents[2]
        project_root = _find_project_root()
        logger.info(f"[AndroidLaunch] __file__={Path(__file__).resolve()} cwd={Path.cwd()} project_root={project_root}")

        # Allow overrides via environment variables
        env_android_script = os.environ.get('ANDROID_LAUNCH_SCRIPT')
        env_python = os.environ.get('ANDROID_LAUNCH_PYTHON')

        android_script = _resolve_launch_script('run_android.py', env_android_script)
        if not android_script:
            expected = Path(__file__).parent.parent / 'temp_build_final' / 'obfuscated' / 'run_android.py'
            try:
                from utils.errors import SECURE_BUILD_INCOMPLETE_TITLE
            except Exception:
                SECURE_BUILD_INCOMPLETE_TITLE = 'Secure Build Incomplete'
            logger.critical("[SECURITY] No valid Android launcher found. Expected at least %s", expected)
            return render_template(
                'error_secure_build.html',
                expected_path=str(expected),
                details='Contact administrator to rebuild secure distribution',
                title=SECURE_BUILD_INCOMPLETE_TITLE
            ), 500

        logger.warning(
            "[AndroidLaunch] EXECUTING SCRIPT: %s (strategy=%s)",
            android_script,
            os.getenv('SECURE_BUILD_OBFUSCATION', 'unknown')
        )

        shared_module = _ensure_shared_directory_module(android_script)
        runtime_root = (android_script.parent / 'runtime').resolve()

        if android_script:
            try:
                # Import dynamic port management
                sys.path.insert(0, str(Path(__file__).parent.parent.parent))
                try:
                    from utils.port_manager import get_port_manager, ensure_platform_ports
                    port_manager = get_port_manager()

                    # Clean up stale state and ensure ports are available for Android
                    cleaned = port_manager.cleanup_stale_processes()
                    logger.info(f"PortManager cleanup_stale_processes updated entries: {cleaned}")

                    # Allocate and ensure ports for Android platform (dynamic)
                    # Start from request hints if provided, but PortManager will find free ports if occupied
                    hints = {}
                    try:
                        hints['flask'] = int(request.args.get('port')) if request.args.get('port') else None
                    except Exception:
                        pass
                    try:
                        hints['appium'] = int(request.args.get('appium_port')) if request.args.get('appium_port') else None
                    except Exception:
                        pass
                    try:
                        hints['wda'] = int(request.args.get('wda_port')) if request.args.get('wda_port') else None
                    except Exception:
                        pass
                    hints = {k:v for k,v in hints.items() if v}
                    android_cfg = ensure_platform_ports('android', hints if hints else None)
                    android_port = android_cfg.flask_port
                    appium_port = android_cfg.appium_port
                    wda_port = android_cfg.wda_port

                    logger.info(f"Ensured Android ports: Flask={android_port}, Appium={appium_port}, WDA={wda_port}")

                except ImportError:
                    # Fallback to static ports if port manager not available
                    logger.warning("Port manager not available, using static ports")
                    android_port = int(request.args.get('port', 8091))
                    appium_port = int(request.args.get('appium_port', 4724))
                    wda_port = int(request.args.get('wda_port', 8300))

                # Preemptively kill blockers on desired ports (best-effort)
                killed = []
                for p in (android_port, appium_port, wda_port):
                    k = _kill_process_on_port(p)
                    if k:
                        killed.extend([(p, pid) for pid in k])
                if killed:
                    logger.warning(f"Killed existing listeners: {killed}")
                # After PortManager selection, ports should be free, but double-check and re-select if collision persists
                from utils.port_manager import get_port_manager
                pm = get_port_manager()
                if not pm.is_port_available(android_port):
                    alt = pm.find_available_port('flask', start_port=8091) or _find_free_port(8091, 8111)
                    logger.info(f"Android Flask port {android_port} busy; switching to {alt}")
                    android_port = alt or android_port
                if not pm.is_port_available(appium_port):
                    alt = pm.find_available_port('appium', start_port=4724) or _find_free_port(4724, 4734)
                    logger.info(f"Android Appium port {appium_port} busy; switching to {alt}")
                    appium_port = alt or appium_port
                if not pm.is_port_available(wda_port):
                    alt = pm.find_available_port('wda', start_port=8300) or _find_free_port(8300, 8310)
                    logger.info(f"Android WDA port {wda_port} busy; switching to {alt}")
                    wda_port = alt or wda_port

                # Resolve Python interpreter for launching the platform app
                # Prefer the project's venv python if present, else system python3. Allow env override.
                import shutil as _sh
                venv_python = project_root / 'venv' / 'bin' / 'python3'
                python_candidates = [
                    env_python,
                    str(venv_python) if venv_python.exists() else None,
                    _sh.which('python3'),
                    '/usr/bin/python3' if sys.platform == 'darwin' and Path('/usr/bin/python3').exists() else None,
                    _sh.which('python'),
                    sys.executable,
                ]
                python_candidates = [c for c in python_candidates if c]
                python_executable = python_candidates[0]
                logger.info(f"[AndroidLaunch] Selected Python interpreter: {python_executable}")

                # Create launch command with dynamic ports
                launch_command = [
                    python_executable,
                    str(android_script),
                    '--use-static-ports',
                    '--port', str(android_port),
                    '--appium-port', str(appium_port),
                    '--wda-port', str(wda_port)
                ]

                logger.info(f"[AndroidLaunch] Using script: {android_script}")
                logger.info(f"[AndroidLaunch] Launch command: {' '.join(launch_command)}")

                # Set environment variables for the Android app
                env = os.environ.copy()
                env['ANDROID_FLASK_PORT'] = str(android_port)
                env['ANDROID_APPIUM_PORT'] = str(appium_port)
                env['ANDROID_WDA_PORT'] = str(wda_port)
                env['SECURE_BUILD'] = 'True'
                env['AUTOMATION_WORKSPACE'] = str(runtime_root)
                # Do NOT add project_root to PYTHONPATH for Android in secure mode

                pythonpath_entries = [
                    str(android_script.parent),
                    str(shared_module.parent),
                    str(Path(__file__).resolve().parents[1]),
                ]
                existing_pythonpath = env.get('PYTHONPATH')
                if existing_pythonpath:
                    pythonpath_entries.append(existing_pythonpath)
                env['PYTHONPATH'] = os.pathsep.join(filter(None, pythonpath_entries))

                # Launch the process
                # Stream logs to file to avoid PIPE buffer deadlocks
                logs_dir = Path(__file__).parent.parent / 'logs'
                logs_dir.mkdir(parents=True, exist_ok=True)
                log_file_path = logs_dir / f'android_launch_{datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")}.log'
                log_file = open(log_file_path, 'a')
                process = subprocess.Popen(
                    launch_command,
                    cwd=str(android_script.parent),
                    env=env,
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    text=True
                )

                # Give it a moment to start
                import time
                time.sleep(3)

                # Check if process is still running
                if process.poll() is None:
                    log_file.flush()
                    log_file.close()
                    flash(f'Android automation tools launched successfully on port {android_port}!', 'success')
                    logger.info("Android app launched successfully")

                    # Store process info for cleanup and discovery
                    session['android_process_pid'] = process.pid
                    session['android_port'] = android_port
                    session['android_appium_port'] = appium_port
                    session['android_wda_port'] = wda_port
                    # Analytics: mark platform launched
                    try:
                        record_platform_launched(session.get('user_id'), 'android')
                    except Exception:
                        pass

                    # Create a launch confirmation page with session token
                    access_token = session.get('access_token')
                    dashboard_url = f"{request.scheme}://{request.host}/dashboard"
                    android_url = f'http://localhost:{android_port}/?session_token={access_token}&return_url={dashboard_url}&user_email={user.user.email}'

                    return render_template_string("""
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Android App Launched</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
                        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
                        <script>
                            // Wait for Android app port from server-side to avoid browser network errors
                            let checks = 0;
                            const maxChecks = 120; // up to ~120s to allow Appium startup
                            function checkAndroidApp() {
                                fetch('/api/wait_for_port?port={{ android_port }}')
                                    .then(resp => {
                                        if (resp.status === 200) {
                                            // Ready - enable manual open button instead of auto-opening
                                            const btn = document.getElementById('open-android-btn');
                                            if (btn) {
                                                btn.removeAttribute('disabled');
                                                btn.classList.remove('btn-secondary');
                                                btn.classList.add('btn-success');
                                            }
                                            document.getElementById('status-text').textContent = 'Ready - click "Open Android App" to continue';
                                        } else if (checks++ < maxChecks) {
                                            setTimeout(checkAndroidApp, 1000);
                                        } else {
                                            // Fallback: allow manual open after timeout
                                            const btn = document.getElementById('open-android-btn');
                                            if (btn) {
                                                btn.removeAttribute('disabled');
                                                btn.classList.remove('btn-secondary');
                                                btn.classList.add('btn-warning');
                                            }
                                            document.getElementById('status-text').textContent = 'Manual open available (port check timed out)';
                                        }
                                    })
                                    .catch(() => {
                                        if (checks++ < maxChecks) setTimeout(checkAndroidApp, 1000);
                                    });
                            }
                            setTimeout(checkAndroidApp, 1000);
                        </script>
                    </head>
                    <body class="bg-light">
                        <div class="container mt-5">
                            <div class="row justify-content-center">
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <div class="mb-4">
                                                <i class="bi bi-android2" style="font-size: 4rem; color: #3DDC84;"></i>
                                            </div>
                                            <h3 class="card-title text-success">Android App Launched Successfully!</h3>
                                            <p class="card-text">The Android automation platform is starting up...</p>
                                            <div class="alert alert-info">
                                                <strong>Port:</strong> {{ android_port }}<br>
                                                <strong>Appium Port:</strong> {{ appium_port }}<br>
                                                <strong>User:</strong> {{ user_email }}<br>
                                                <strong>Status:</strong> <span id="status-text">Starting up...</span>
                                            </div>
                                            <div class="mb-3">
                                                <div class="spinner-border text-success" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </div>
                                            <div class="text-muted">Auto-open disabled to prevent pop-ups. Use the button below when ready.</div>
                                            <div class="d-grid gap-2 mt-2">
                                                <a id="open-android-btn" href="{{ android_url }}" class="btn btn-secondary" target="_blank" rel="noopener" disabled>
                                                    <i class="bi bi-box-arrow-up-right"></i> Open Android App
                                                </a>
                                                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                                                    <i class="bi bi-arrow-left"></i> Back to Dashboard
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </body>
                    </html>
                    """, android_port=android_port, appium_port=appium_port, user_email=user.user.email, android_url=android_url)
                else:
                    # Process failed to start
                    process.wait(timeout=5)
                    log_file.flush()
                    log_file.close()
                    error_msg = f"Process failed to start. Exit code: {process.returncode}"
                    try:
                        with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as lf:
                            tail = ''.join(lf.readlines()[-40:]).strip()
                        if tail:
                            error_msg += f"\nLog tail:\n{tail}"
                    except Exception as log_err:
                        logger.warning(f"Unable to read Android launch log: {log_err}")
                    logger.error(f"Android app launch failed: {error_msg}")
                    flash(f'Failed to launch Android tools: {error_msg}', 'error')

            except Exception as e:
                logger.error(f"Failed to launch Android script: {e}")
                flash(f'Failed to launch Android tools: {e}', 'error')
        else:
            logger.error(f"Android script not found at: {android_script}")
            flash('Android automation tools not found. Please check the installation.', 'warning')

        # If we get here, something went wrong, redirect back to dashboard
        return redirect(url_for('dashboard'))

    except Exception as e:
        logger.error(f"Android app launch error: {e}")
        flash(f'Error launching Android app: {e}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/app_ios')
@app.route('/app_ios/<session_id>')
def app_ios(session_id=None):
    """Launch iOS automation app"""
    try:
        user = None
        if 'access_token' not in session:
            user, err = _ensure_session_from_authorization()
            if not user:
                flash('Please log in to access applications.', 'warning')
                return redirect(url_for('login_page'))
        else:
            # Validate Supabase session and fallback
            try:
                user = supabase.auth.get_user(session['access_token'])
                if not user:
                    flash('Session expired. Please log in again.', 'warning')
                    session.clear()
                    return redirect(url_for('login_page'))
            except Exception:
                user, err = _ensure_session_from_authorization()
                if not user:
                    flash('Session validation failed. Please log in again.', 'error')
                    session.clear()
                    return redirect(url_for('login_page'))

        # Enforce license compliance before allowing access
        if license_manager:
            try:
                license_valid = license_manager.enforce_license_compliance(user.user.id)
                if not license_valid:
                    validation_result = license_manager.validate_license(user.user.id)
                    error_msg = validation_result.get('error', 'License validation failed')
                    flash(f'Access denied: {error_msg}', 'error')
                    session.clear()
                    return redirect(url_for('login_page'))

                # Update last validation timestamp
                license_manager.update_last_validation(user.user.id)

            except Exception as e:
                logger.error(f"License enforcement error: {e}")
                flash('License validation error. Please contact support.', 'error')
                session.clear()
                return redirect(url_for('login_page'))

        logger.info(f"Launching iOS app for user: {user.user.email}")

        env_ios_script = os.environ.get('IOS_LAUNCH_SCRIPT')
        ios_script = _resolve_launch_script('run.py', env_ios_script)

        if not ios_script:
            expected = Path(__file__).parent.parent / 'temp_build_final' / 'obfuscated' / 'run.py'
            try:
                from utils.errors import SECURE_BUILD_INCOMPLETE_TITLE
            except Exception:
                SECURE_BUILD_INCOMPLETE_TITLE = 'Secure Build Incomplete'
            logger.critical("[SECURITY] No valid iOS launcher found. Expected at least %s", expected)
            return render_template(
                'error_secure_build.html',
                expected_path=str(expected),
                details='Contact administrator to rebuild secure distribution',
                title=SECURE_BUILD_INCOMPLETE_TITLE
            ), 500

        logger.warning(
            "[iOSLaunch] EXECUTING SCRIPT: %s (strategy=%s)",
            ios_script,
            os.getenv('SECURE_BUILD_OBFUSCATION', 'unknown')
        )

        shared_module = _ensure_shared_directory_module(ios_script)
        runtime_root = (ios_script.parent / 'runtime').resolve()

        if ios_script and ios_script.exists():
            try:
                # Import dynamic port management
                sys.path.insert(0, str(Path(__file__).parent.parent.parent))
                try:
                    from utils.port_manager import get_port_manager, ensure_platform_ports
                    port_manager = get_port_manager()

                    # Clean up stale state and ensure ports are available for iOS
                    cleaned = port_manager.cleanup_stale_processes()
                    logger.info(f"PortManager cleanup_stale_processes updated entries: {cleaned}")

                    # Allocate and ensure ports for iOS platform (dynamic)
                    hints = {}
                    try:
                        hints['flask'] = int(request.args.get('port')) if request.args.get('port') else None
                    except Exception:
                        pass
                    try:
                        hints['appium'] = int(request.args.get('appium_port')) if request.args.get('appium_port') else None
                    except Exception:
                        pass
                    try:
                        hints['wda'] = int(request.args.get('wda_port')) if request.args.get('wda_port') else None
                    except Exception:
                        pass
                    hints = {k:v for k,v in hints.items() if v}
                    ios_cfg = ensure_platform_ports('ios', hints if hints else None)
                    ios_port = ios_cfg.flask_port
                    appium_port = ios_cfg.appium_port
                    wda_port = ios_cfg.wda_port

                    logger.info(f"Ensured iOS ports: Flask={ios_port}, Appium={appium_port}, WDA={wda_port}")

                except ImportError:
                    # Fallback to static ports if port manager not available
                    logger.warning("Port manager not available, using static ports")
                    ios_port = int(request.args.get('port', 8090))
                    appium_port = int(request.args.get('appium_port', 4723))
                    wda_port = int(request.args.get('wda_port', 8100))

                # Preemptively kill blockers on desired ports (best-effort)
                killed = []
                for p in (ios_port, appium_port, wda_port):
                    k = _kill_process_on_port(p)
                    if k:
                        killed.extend([(p, pid) for pid in k])
                if killed:
                    logger.warning(f"Killed existing listeners: {killed}")
                # After PortManager selection, ports should be free, but double-check and re-select if collision persists
                from utils.port_manager import get_port_manager
                pm = get_port_manager()
                if not pm.is_port_available(ios_port):
                    alt = pm.find_available_port('flask', start_port=8090) or _find_free_port(8090, 8110)
                    logger.info(f"iOS Flask port {ios_port} busy; switching to {alt}")
                    ios_port = alt or ios_port
                if not pm.is_port_available(appium_port):
                    alt = pm.find_available_port('appium', start_port=4723) or _find_free_port(4723, 4733)
                    logger.info(f"iOS Appium port {appium_port} busy; switching to {alt}")
                    appium_port = alt or appium_port
                if not pm.is_port_available(wda_port):
                    alt = pm.find_available_port('wda', start_port=8100) or _find_free_port(8100, 8110)
                    logger.info(f"iOS WDA port {wda_port} busy; switching to {alt}")
                    wda_port = alt or wda_port

                # Resolve Python interpreter for launching the platform app
                # Always prefer system Python over the bundled executable to avoid PyInstaller temp paths
                import shutil as _sh
                candidates = [
                    _sh.which('python3'),
                    '/usr/bin/python3' if sys.platform == 'darwin' and Path('/usr/bin/python3').exists() else None,
                    _sh.which('python'),
                ]
                python_executable = next((c for c in candidates if c), sys.executable)
                logger.info(f"Selected Python interpreter: {python_executable}")

                # Create launch command with dynamic ports
                launch_command = [
                    python_executable,
                    str(ios_script),
                    '--use-static-ports',
                    '--flask-port', str(ios_port),
                    '--appium-port', str(appium_port),
                    '--wda-port', str(wda_port)
                ]

                logger.info(f"Launching iOS app with command: {' '.join(launch_command)}")

                # Set environment variables for the iOS app
                env = os.environ.copy()
                env['IOS_FLASK_PORT'] = str(ios_port)
                env['IOS_APPIUM_PORT'] = str(appium_port)
                env['IOS_WDA_PORT'] = str(wda_port)
                env['SECURE_BUILD'] = 'True'
                env['AUTOMATION_WORKSPACE'] = str(runtime_root)

                pythonpath_entries = [
                    str(ios_script.parent),
                    str(shared_module.parent),
                    str(Path(__file__).resolve().parents[1]),
                ]
                existing_pythonpath = env.get('PYTHONPATH')
                if existing_pythonpath:
                    pythonpath_entries.append(existing_pythonpath)
                env['PYTHONPATH'] = os.pathsep.join(filter(None, pythonpath_entries))

                # Provide a minimal config shim for iOS so obfuscated run.py can import "config"
                shims_dir = Path(__file__).parent.parent / 'temp_build_final' / 'obfuscated' / 'tmp' / 'import_shims'
                try:
                    shims_dir.mkdir(parents=True, exist_ok=True)
                    shim_code = """
import os
from pathlib import Path

_BASE_DIR = Path(__file__).resolve().parent.parent

class FlaskConfig:
    SECRET_KEY = os.getenv('FLASK_SECRET_KEY', 'mobile-automation-secret-key')
    SESSION_COOKIE_NAME = 'ios_session'
    SESSION_COOKIE_SAMESITE = 'Lax'
    SESSION_COOKIE_SECURE = False
    TESTING = False
    DEBUG = False
    PORT = int(os.getenv('IOS_FLASK_PORT', os.getenv('FLASK_PORT', '8090')))
    HOST = '127.0.0.1' if os.getenv('SECURE_BUILD', 'False').lower() == 'true' else '0.0.0.0'

DEFAULT_FLASK_PORT = int(os.getenv('IOS_FLASK_PORT', 8090))
DEFAULT_APPIUM_PORT = int(os.getenv('IOS_APPIUM_PORT', 4723))
DEFAULT_WDA_PORT = int(os.getenv('IOS_WDA_PORT', 8200))
FLASK_PORT = DEFAULT_FLASK_PORT
APPIUM_PORT = DEFAULT_APPIUM_PORT
WDA_PORT = DEFAULT_WDA_PORT
APPIUM_CONFIG = {'HOST': '127.0.0.1', 'PORT': APPIUM_PORT, 'BASE_PATH': '/wd/hub'}

DIRECTORIES = {
    'TEST_CASES': (_BASE_DIR / 'test_cases_ios'),
    'REPORTS': (_BASE_DIR / 'reports_ios'),
    'SCREENSHOTS': (_BASE_DIR / 'screenshots_ios'),
    'REFERENCE_IMAGES': (_BASE_DIR / 'reference_images_ios'),
    'TEST_SUITES': (_BASE_DIR / 'test_suites_ios'),
    'RESULTS': (_BASE_DIR / 'reports_ios' / 'suites'),
    'RECORDINGS': (_BASE_DIR / 'recordings_ios'),
    'TEMP_FILES': (_BASE_DIR / 'temp_ios'),
}

FILES_TO_PUSH_DIR = _BASE_DIR / 'files_to_push_ios'

for _dir in list(DIRECTORIES.values()) + [FILES_TO_PUSH_DIR]:
    try:
        Path(_dir).mkdir(parents=True, exist_ok=True)
    except Exception:
        pass

GLOBAL_VALUES = {
    'Auto Rerun Failed': False,
    'Connection Retry Attempts': 3,
    'Connection Retry Delay': 2,
    'Max Step Execution Time': 300,
    'Test Case Delay': 15,
    'Test Run Retry': 3,
    'default_element_timeout': 60,
}
"""
                    with open(shims_dir / 'config.py', 'w') as f:
                        f.write(shim_code)
                except Exception as e:
                    logger.error(f"Failed to write iOS config shim: {e}")

                # Ensure shim directory is on PYTHONPATH (not the repo root)
                py_path = env.get('PYTHONPATH', '')
                env['PYTHONPATH'] = f"{shims_dir}:{py_path}" if py_path else str(shims_dir)

                # Launch the process
                # Stream logs to file to avoid PIPE buffer deadlocks
                logs_dir = Path(__file__).parent.parent / 'logs'
                logs_dir.mkdir(parents=True, exist_ok=True)
                log_file_path = logs_dir / f'ios_launch_{datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")}.log'
                log_file = open(log_file_path, 'a')
                process = subprocess.Popen(
                    launch_command,
                    cwd=str(ios_script.parent),
                    env=env,
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    text=True
                )

                # Give it a moment to start
                import time
                time.sleep(2)

                # Check if process is still running
                if process.poll() is None:
                    log_file.flush()
                    log_file.close()
                    flash(f'iOS automation tools launched successfully on port {ios_port}!', 'success')
                    logger.info("iOS app launched successfully")
                    # Store process info for cleanup and discovery
                    session['ios_process_pid'] = process.pid
                    session['ios_port'] = ios_port
                    session['ios_appium_port'] = appium_port
                    session['ios_wda_port'] = wda_port
                    # Analytics: mark platform launched
                    try:
                        record_platform_launched(session.get('user_id'), 'ios')
                    except Exception:
                        pass

                    # Wait a bit longer for the iOS app to fully start
                    import time
                    time.sleep(3)

                    # Check if iOS app is responding
                    import socket
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(2)
                    ios_app_ready = sock.connect_ex(('localhost', ios_port)) == 0
                    sock.close()

                    if ios_app_ready:
                        # iOS app is ready, redirect immediately
                        logger.info(f"iOS app is ready on port {ios_port}, redirecting user")

                        # Store session info for iOS app to access
                        session['ios_app_launched'] = True
                        session['ios_app_port'] = ios_port
                        session['launch_timestamp'] = datetime.now(timezone.utc).isoformat()

                        # Create session token for iOS app
                        access_token = session.get('access_token')
                        dashboard_url = f"{request.scheme}://{request.host}/dashboard"

                        # Open iOS app in a new tab to keep dashboard open
                        ios_url = f'http://localhost:{ios_port}/?session_token={access_token}&return_url={dashboard_url}&user_email={user.user.email}'
                        return render_template_string(
                            """
                            <!DOCTYPE html>
                            <html>
                            <head>
                                <title>iOS App Ready</title>
                                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
                                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
                                <script>
                                    window.addEventListener('load', function() {
                                        // Auto-open disabled to avoid repeated browser tabs. Use the button below.
                                        const btn = document.getElementById('open-ios-btn');
                                        if (btn) {
                                            btn.removeAttribute('disabled');
                                            btn.classList.remove('btn-secondary');
                                            btn.classList.add('btn-primary');
                                        }
                                    });
                                </script>
                            </head>
                            <body class="bg-light">
                                <div class="container mt-5">
                                    <div class="row justify-content-center">
                                        <div class="col-md-8">
                                            <div class="card">
                                                <div class="card-body text-center">
                                                    <div class="mb-4">
                                                        <i class="bi bi-apple" style="font-size: 4rem; color: #007AFF;"></i>
                                                    </div>
                                                    <h3 class="card-title text-primary">iOS App Launched</h3>
                                                    <p class="card-text">The iOS automation platform has opened in a new tab.</p>
                                                    <div class="d-grid gap-2">
                                                        <a href="{{ ios_url }}" target="_blank" class="btn btn-primary">
                                                            <i class="bi bi-box-arrow-up-right"></i> Open iOS App
                                                        </a>
                                                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                                                            <i class="bi bi-arrow-left"></i> Back to Dashboard
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </body>
                            </html>
                            """,
                            ios_url=ios_url
                        )
                    else:
                        # iOS app not ready yet, show loading page with auto-redirect
                        logger.info(f"iOS app starting on port {ios_port}, showing loading page")

                        return render_template_string("""
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>Launching iOS Automation Platform</title>
                            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
                            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
                            <script>
                                let checkCount = 0;
                                const maxChecks = 20; // 20 seconds max

                                function checkiOSApp() {
                                    checkCount++;
                                    fetch('/api/wait_for_port?port={{ ios_port }}')
                                        .then((resp) => {
                                            if (resp.status === 200) {
                                                // iOS app is ready; enable manual open button instead of auto-opening
                                                const btn = document.getElementById('open-ios-btn');
                                                if (btn) {
                                                    btn.href = 'http://localhost:{{ ios_port }}/?session_token={{ access_token }}&return_url={{ dashboard_url }}&user_email={{ user_email }}';
                                                    btn.removeAttribute('disabled');
                                                    btn.classList.remove('btn-secondary');
                                                    btn.classList.add('btn-primary');
                                                }
                                                document.getElementById('loading').style.display = 'none';
                                                document.getElementById('manual').style.display = 'block';
                                            } else {
                                                throw new Error('not ready');
                                            }
                                        })
                                        .catch(() => {
                                            // iOS app not ready yet
                                            if (checkCount < maxChecks) {
                                                setTimeout(checkiOSApp, 1000);
                                            } else {
                                                // Timeout, show manual link
                                                document.getElementById('loading').style.display = 'none';
                                                document.getElementById('manual').style.display = 'block';
                                            }
                                        });
                                }

                                // Start checking after 2 seconds
                                setTimeout(checkiOSApp, 2000);
                            </script>
                        </head>
                        <body class="bg-light">
                            <div class="container mt-5">
                                <div class="row justify-content-center">
                                    <div class="col-md-8">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <div id="loading">
                                                    <div class="mb-4">
                                                        <i class="bi bi-apple" style="font-size: 4rem; color: #007AFF;"></i>
                                                    </div>
                                                    <h3 class="card-title text-primary">Launching iOS Automation Platform</h3>
                                                    <p class="card-text">Initializing iOS testing environment...</p>
                                                    <div class="alert alert-info">
                                                        <strong>Port:</strong> {{ ios_port }}<br>
                                                        <strong>User:</strong> {{ user_email }}<br>
                                                        <strong>Status:</strong> Starting up...
                                                    </div>
                                                    <div class="mb-3">
                                                        <div class="spinner-border text-primary" role="status">
                                                            <span class="visually-hidden">Loading...</span>
                                                        </div>
                                                    </div>
                                                    <p class="text-muted">You will be redirected automatically when ready...</p>
                                                </div>

                                                <div id="manual" style="display: none;">
                                                    <div class="mb-4">
                                                        <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: #ffc107;"></i>
                                                    </div>
                                                    <h4 class="card-title text-warning">iOS App Taking Longer Than Expected</h4>
                                                    <p class="card-text">The iOS automation platform is still starting up.</p>
                                                    <div class="d-grid gap-2">
                                                        <a href="http://localhost:{{ ios_port }}/?session_token={{ access_token }}&return_url={{ dashboard_url }}&user_email={{ user_email }}" class="btn btn-primary" target="_blank">
                                                            <i class="bi bi-box-arrow-up-right"></i> Open iOS App Manually
                                                        </a>
                                                        <button onclick="location.reload()" class="btn btn-outline-primary">
                                                            <i class="bi bi-arrow-clockwise"></i> Check Again
                                                        </button>
                                                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                                                            <i class="bi bi-arrow-left"></i> Back to Dashboard
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </body>
                        </html>
                        """,
                        ios_port=ios_port,
                        user_email=user.user.email,
                        access_token=session.get('access_token'),
                        dashboard_url=f"{request.scheme}://{request.host}/dashboard")
                else:
                    # Process failed to start
                    process.wait(timeout=5)
                    log_file.flush()
                    log_file.close()
                    error_msg = f"Process failed to start. Exit code: {process.returncode}"
                    try:
                        with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as lf:
                            tail = ''.join(lf.readlines()[-40:]).strip()
                        if tail:
                            error_msg += f"\nLog tail:\n{tail}"
                    except Exception as log_err:
                        logger.warning(f"Unable to read iOS launch log: {log_err}")
                    logger.error(f"iOS app launch failed: {error_msg}")
                    flash(f'Failed to launch iOS tools: {error_msg}', 'error')

            except Exception as e:
                logger.error(f"Failed to launch iOS script: {e}")
                flash(f'Failed to launch iOS tools: {e}', 'error')
        else:
            logger.error(f"iOS script not found at: {ios_script}")
            flash('iOS automation tools not found. Please check the installation.', 'warning')

        # If we get here, something went wrong, redirect back to dashboard
        return redirect(url_for('dashboard'))

    except Exception as e:
        logger.error(f"iOS app launch error: {e}")
        flash(f'Error launching iOS app: {e}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/logout')

@app.post('/api/ports/cleanup')
def api_ports_cleanup():
    """Kill known platform listeners and run PortManager cleanup if available."""
    try:
        killed = []
        details = []
        # Try PortManager cleanup first
        try:
            from utils.port_manager import get_port_manager
            pm = get_port_manager()
            cleaned = pm.cleanup_stale_processes()
            details.append({'port_manager_cleaned': cleaned})
        except Exception as e:
            details.append({'port_manager_error': str(e)})
        # Kill default and session-known ports
        target_ports = set([8090, 8091, 4723, 4724, 8200, 8300])
        sess_ios = session.get('ios_port'); sess_android = session.get('android_port')
        if isinstance(sess_ios, int):
            target_ports.add(sess_ios)
        if isinstance(sess_android, int):
            target_ports.add(sess_android)
        for p in sorted(target_ports):
            pids = _kill_process_on_port(p)
            if pids:
                killed.extend([(p, pid) for pid in pids])
        return jsonify({'released': killed, 'details': details})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.post('/api/instances/stop/<platform>')
def api_instances_stop(platform: str):
    """Stop a running platform instance by platform name ('ios' or 'android').
    - Retrieves PID from session
    - Attempts graceful termination
    - Clears related session variables
    - Returns JSON with result details
    """
    try:
        plat = (platform or '').lower()
        if plat not in ('ios', 'android'):
            return jsonify({'success': False, 'error': 'Invalid platform'}), 400

        pid_key = f'{plat}_process_pid'
        pid = session.get(pid_key)
        details = {}
        killed = False
        message = None

        if not pid:
            message = 'No PID found in session; nothing to stop'
        else:
            try:
                import signal, time
                # Attempt graceful termination depending on OS
                if sys.platform.startswith('win'):
                    import subprocess as sp
                    cp = sp.run(['taskkill', '/PID', str(pid), '/T', '/F'], capture_output=True, text=True)
                    details['taskkill_returncode'] = cp.returncode
                    if cp.stdout:
                        details['stdout'] = cp.stdout[:500]
                    if cp.stderr:
                        details['stderr'] = cp.stderr[:500]
                    killed = (cp.returncode == 0)
                else:
                    # POSIX: send SIGTERM, wait, then SIGKILL if needed
                    try:
                        os.kill(pid, signal.SIGTERM)
                    except ProcessLookupError:
                        killed = True  # already gone
                    if not killed:
                        waited = 0.0
                        while waited < 5.0:
                            try:
                                os.kill(pid, 0)
                                time.sleep(0.5)
                                waited += 0.5
                            except ProcessLookupError:
                                killed = True
                                break
                        if not killed:
                            try:
                                os.kill(pid, signal.SIGKILL)
                            except Exception as e:
                                details['sigkill_error'] = str(e)
                            # Best-effort check
                            try:
                                os.kill(pid, 0)
                            except ProcessLookupError:
                                killed = True
            except Exception as e:
                details['terminate_error'] = str(e)

        # Clear session variables regardless
        for key in (f'{plat}_process_pid', f'{plat}_port', f'{plat}_appium_port', f'{plat}_wda_port'):
            try:
                session.pop(key, None)
            except Exception:
                pass

        result = {
            'success': True,
            'platform': plat,
            'killed': killed,
            'message': message,
            'details': details
        }
        return jsonify(result)
    except Exception as e:
        logger.warning(f"api_instances_stop error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


def logout():
    """Logout and redirect to home"""
    session.clear()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('index'))

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return render_template('index.html'), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"Internal server error: {error}")
    return jsonify({'error': 'Internal server error'}), 500

# Health/readiness endpoint
@app.get('/health')
def health():
    return jsonify({
        'status': 'ok',
        'component': 'backend-web',
        'time': datetime.now(timezone.utc).isoformat()
    }), 200

def run_server(host='localhost', port=8080, debug=False):
    """Run the Flask server"""
    try:
        logger.info(f"Starting web server on http://{host}:{port}")
        app.run(host=host, port=port, debug=debug, threaded=True, use_reloader=False)
    except Exception as e:
        logger.error(f"Failed to start web server: {e}")
        raise

# Diagnostics: cleanup and port status endpoints (local use)
# Note: api_ports_cleanup is defined above; avoid duplicate definitions here.
# Keeping only ports status and wait_for_port in this block.

@app.get('/api/wait_for_port')
def api_wait_for_port():
    """Wait for a localhost port to be open, without causing browser network errors.

    Returns 200 when open within ~25s; returns 202 if still starting.
    """
    try:
        import socket, time
        port = int(request.args.get('port', '0'))
        if not (1024 <= port <= 65535):
            return jsonify({'error': 'invalid port'}), 400
        deadline = time.time() + 90  # allow slower startups
        while time.time() < deadline:
            # Prefer explicit health check if the app exposes it (both Android and iOS do)
            try:
                import requests as _req
                _resp = _req.get(f'http://127.0.0.1:{port}/health', timeout=0.75)
                if _resp.status_code == 200:
                    return ('', 200)
            except Exception:
                pass
            # Fallback to raw socket connect on common hostnames
            for host in ('127.0.0.1', 'localhost'):
                try:
                    with socket.create_connection((host, port), timeout=0.75):
                        return ('', 200)
                except Exception:
                    continue
            time.sleep(0.5)
        return ('', 202)
    except Exception as e:
        logger.warning(f"wait_for_port error: {e}")
        return ('', 500)

# ---- Grid status/control endpoints ------------------------------------------
@app.get('/api/grid/status')
def api_grid_status():
    try:
        # Authentication check (session or Authorization header)
        user = None
        if 'access_token' not in session:
            user, err = _ensure_session_from_authorization()
            if not user:
                return jsonify({'error': err or 'Not authenticated'}), 401
        else:
            try:
                user = supabase.auth.get_user(session['access_token'])
                if not user:
                    return jsonify({'error': 'Invalid session'}), 401
            except Exception:
                user, err = _ensure_session_from_authorization()
                if not user:
                    return jsonify({'error': err or 'Not authenticated'}), 401

        if not GRID_AVAILABLE:
            return jsonify({'grid_available': False, 'hub_ready': False, 'devices': [], 'counts': {'ios': 0, 'android': 0}}), 200

        mgr = get_grid_manager()
        health = mgr.health_check()
        hub_ready = mgr.is_hub_ready()
        devices = []
        ios_count = 0
        android_count = 0
        for d in mgr.get_device_info():
            devices.append({
                'platform': d.platform.value,
                'device_name': d.device_name,
                'platform_version': d.platform_version,
                'status': d.status.value,
                'active_sessions': d.active_sessions,
                'max_sessions': d.max_sessions,
                'node_url': d.node_url,
            })
            if d.platform.value.lower() == 'ios' and d.status == NodeStatus.ONLINE:
                ios_count += 1
            if d.platform.value.lower() == 'android' and d.status == NodeStatus.ONLINE:
                android_count += 1

        return jsonify({
            'grid_available': True,
            'hub_ready': hub_ready,
            'health': health,
            'devices': devices,
            'counts': {
                'ios': ios_count,
                'android': android_count
            }
        })
    except Exception as e:
        logger.error(f"/api/grid/status error: {e}")
        return jsonify({'error': str(e)}), 500


@app.get('/api/grid/devices')
def api_grid_devices():
    try:
        # auth (read-only)
        if 'access_token' not in session:
            user, err = _ensure_session_from_authorization()
            if not user:
                return jsonify({'error': err or 'Not authenticated'}), 401
        if not GRID_AVAILABLE:
            return jsonify({'grid_available': False, 'devices': []}), 200
        mgr = get_grid_manager()
        out = []
        for d in mgr.get_device_info():
            out.append({
                'platform': d.platform.value,
                'device_name': d.device_name,
                'platform_version': d.platform_version,
                'status': d.status.value,
                'active_sessions': d.active_sessions,
                'max_sessions': d.max_sessions,
                'node_url': d.node_url,
            })
        return jsonify({'grid_available': True, 'devices': out})
    except Exception as e:
        logger.error(f"/api/grid/devices error: {e}")
        return jsonify({'error': str(e)}), 500


@app.post('/api/grid/start')
def api_grid_start():
    try:
        # privileged action requires auth
        if 'access_token' not in session:
            user, err = _ensure_session_from_authorization()
            if not user:
                return jsonify({'error': err or 'Not authenticated'}), 401
        if not GRID_AVAILABLE:
            return jsonify({'error': 'Grid manager unavailable'}), 501
        mgr = get_grid_manager()
        ok, msg = mgr.start_grid()
        return jsonify({'success': ok, 'message': msg}), (200 if ok else 500)
    except Exception as e:
        logger.error(f"/api/grid/start error: {e}")
        return jsonify({'error': str(e)}), 500


@app.post('/api/grid/stop')
def api_grid_stop():
    try:
        if 'access_token' not in session:
            user, err = _ensure_session_from_authorization()
            if not user:
                return jsonify({'error': err or 'Not authenticated'}), 401
        if not GRID_AVAILABLE:
            return jsonify({'error': 'Grid manager unavailable'}), 501
        mgr = get_grid_manager()
        ok, msg = mgr.stop_grid()
        return jsonify({'success': ok, 'message': msg}), (200 if ok else 500)
    except Exception as e:
        logger.error(f"/api/grid/stop error: {e}")
        return jsonify({'error': str(e)}), 500

@app.get('/api/ports/status')
def api_ports_status():
    try:
        from utils.port_manager import get_port_manager
        pm = get_port_manager()
        return jsonify(pm.get_system_status())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.get('/api/ports/active')
def api_ports_active():
    """Return known active platform ports based on session variables.
    Useful for native clients when Port Manager is not available.
    """
    try:
        return jsonify({
            'ios': session.get('ios_port'),
            'ios_appium': session.get('ios_appium_port'),
            'ios_wda': session.get('ios_wda_port'),
            'android': session.get('android_port'),
            'android_appium': session.get('android_appium_port'),
            'android_wda': session.get('android_wda_port'),
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Get host and port from environment or use defaults
    host = os.getenv('FLASK_HOST', 'localhost')
    port = int(os.getenv('FLASK_PORT', 8080))
    debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

    if not WEB_SILENT:
        logger.info("Starting Flask web server...")
        logger.info(f"URL: http://{host}:{port}")
        logger.info(f"Login page: http://{host}:{port}/login")
        logger.info(f"Dashboard: http://{host}:{port}/dashboard")

    run_server(host=host, port=port, debug=debug)
